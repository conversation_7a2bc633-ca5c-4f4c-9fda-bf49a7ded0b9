using System;
using System.Diagnostics;
using System.IO;
using System.ServiceProcess;
using System.Threading;
using System.Threading.Tasks;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;

namespace VMSWindowsService
{
    /// <summary>
    /// VMS Windows Service - Automated deployment service for VMS system
    /// Handles boot-time startup, port reservation, and process management
    /// </summary>
    public partial class VMSWindowsService : ServiceBase
    {
        private Process _vmsProcess;
        private Timer _healthCheckTimer;
        private Timer _portReservationTimer;
        private readonly string _serviceName = "VMS-Production-Service";
        private readonly int _targetPort = 8080;
        private string _vmsPath;
        private string _logPath;
        private bool _isShuttingDown = false;

        public VMSWindowsService()
        {
            InitializeComponent();
            ServiceName = _serviceName;
            
            // Detect VMS installation path dynamically
            _vmsPath = DetectVMSPath();
            _logPath = Path.Combine(_vmsPath, "Logs", "service.log");
            
            // Ensure log directory exists
            Directory.CreateDirectory(Path.GetDirectoryName(_logPath));
            
            LogMessage("VMS Windows Service initialized");
            LogMessage($"VMS Path: {_vmsPath}");
        }

        /// <summary>
        /// Dynamically detect VMS installation path
        /// </summary>
        private string DetectVMSPath()
        {
            try
            {
                // Method 1: Check service executable location
                string serviceDir = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
                string vmsPath = Path.GetDirectoryName(Path.GetDirectoryName(serviceDir)); // Go up 2 levels
                
                if (IsValidVMSPath(vmsPath))
                {
                    return vmsPath;
                }

                // Method 2: Search common locations
                string[] searchPaths = {
                    @"C:\VMS-PRODUCTION",
                    @"C:\Program Files\VMS-PRODUCTION",
                    @"C:\Program Files (x86)\VMS-PRODUCTION",
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "VMS-PRODUCTION"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "VMS-PRODUCTION")
                };

                foreach (string path in searchPaths)
                {
                    if (IsValidVMSPath(path))
                    {
                        return path;
                    }
                }

                // Method 3: Search all drives
                foreach (DriveInfo drive in DriveInfo.GetDrives())
                {
                    if (drive.IsReady && drive.DriveType == DriveType.Fixed)
                    {
                        string drivePath = Path.Combine(drive.RootDirectory.FullName, "VMS-PRODUCTION");
                        if (IsValidVMSPath(drivePath))
                        {
                            return drivePath;
                        }
                    }
                }

                throw new DirectoryNotFoundException("VMS installation not found");
            }
            catch (Exception ex)
            {
                LogMessage($"Error detecting VMS path: {ex.Message}");
                return @"C:\VMS-PRODUCTION"; // Fallback
            }
        }

        /// <summary>
        /// Validate if path contains VMS installation
        /// </summary>
        private bool IsValidVMSPath(string path)
        {
            try
            {
                return Directory.Exists(path) &&
                       Directory.Exists(Path.Combine(path, "Server")) &&
                       File.Exists(Path.Combine(path, "Server", "dist", "index.js"));
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Service startup
        /// </summary>
        protected override void OnStart(string[] args)
        {
            LogMessage("VMS Service starting...");
            
            try
            {
                // Reserve port 8080
                ReservePort(_targetPort);
                
                // Start VMS server
                StartVMSServer();
                
                // Start health monitoring
                _healthCheckTimer = new Timer(HealthCheck, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(5));
                
                // Start port reservation monitoring
                _portReservationTimer = new Timer(CheckPortReservation, null, TimeSpan.FromMinutes(10), TimeSpan.FromMinutes(30));
                
                LogMessage("VMS Service started successfully");
            }
            catch (Exception ex)
            {
                LogMessage($"Error starting VMS Service: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Start VMS server process
        /// </summary>
        private void StartVMSServer()
        {
            try
            {
                if (_vmsProcess != null && !_vmsProcess.HasExited)
                {
                    LogMessage("VMS server already running");
                    return;
                }

                string serverPath = Path.Combine(_vmsPath, "Server");
                string nodeExe = FindNodeExecutable();
                
                if (string.IsNullOrEmpty(nodeExe))
                {
                    throw new FileNotFoundException("Node.js not found. Please install Node.js.");
                }

                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = nodeExe,
                    Arguments = "dist/index.js",
                    WorkingDirectory = serverPath,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,
                    Environment = {
                        ["NODE_ENV"] = "production",
                        ["PORT"] = _targetPort.ToString(),
                        ["VMS_SERVICE_MODE"] = "true"
                    }
                };

                _vmsProcess = Process.Start(startInfo);
                
                if (_vmsProcess == null)
                {
                    throw new InvalidOperationException("Failed to start VMS server process");
                }

                // Log output asynchronously
                _vmsProcess.OutputDataReceived += (sender, e) => {
                    if (!string.IsNullOrEmpty(e.Data))
                        LogMessage($"VMS: {e.Data}");
                };
                
                _vmsProcess.ErrorDataReceived += (sender, e) => {
                    if (!string.IsNullOrEmpty(e.Data))
                        LogMessage($"VMS ERROR: {e.Data}");
                };

                _vmsProcess.BeginOutputReadLine();
                _vmsProcess.BeginErrorReadLine();

                LogMessage($"VMS server started with PID: {_vmsProcess.Id}");
            }
            catch (Exception ex)
            {
                LogMessage($"Error starting VMS server: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Find Node.js executable
        /// </summary>
        private string FindNodeExecutable()
        {
            try
            {
                // Check PATH environment variable
                string pathEnv = Environment.GetEnvironmentVariable("PATH");
                if (!string.IsNullOrEmpty(pathEnv))
                {
                    foreach (string path in pathEnv.Split(';'))
                    {
                        string nodeExe = Path.Combine(path.Trim(), "node.exe");
                        if (File.Exists(nodeExe))
                        {
                            return nodeExe;
                        }
                    }
                }

                // Check common installation paths
                string[] commonPaths = {
                    @"C:\Program Files\nodejs\node.exe",
                    @"C:\Program Files (x86)\nodejs\node.exe",
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "nodejs", "node.exe"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "nodejs", "node.exe")
                };

                foreach (string path in commonPaths)
                {
                    if (File.Exists(path))
                    {
                        return path;
                    }
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Reserve port 8080 to prevent other applications from using it
        /// </summary>
        private void ReservePort(int port)
        {
            try
            {
                // Check if port is already in use by VMS
                if (IsPortInUseByVMS(port))
                {
                    LogMessage($"Port {port} already in use by VMS - OK");
                    return;
                }

                // Check if port is in use by another application
                if (IsPortInUse(port))
                {
                    LogMessage($"WARNING: Port {port} is in use by another application");
                    // Try to free the port (kill conflicting processes)
                    KillProcessesUsingPort(port);
                }

                LogMessage($"Port {port} reserved for VMS");
            }
            catch (Exception ex)
            {
                LogMessage($"Error reserving port {port}: {ex.Message}");
            }
        }

        /// <summary>
        /// Check if port is in use
        /// </summary>
        private bool IsPortInUse(int port)
        {
            try
            {
                IPGlobalProperties ipGlobalProperties = IPGlobalProperties.GetIPGlobalProperties();
                IPEndPoint[] tcpConnInfoArray = ipGlobalProperties.GetActiveTcpListeners();

                foreach (IPEndPoint endpoint in tcpConnInfoArray)
                {
                    if (endpoint.Port == port)
                    {
                        return true;
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Check if port is in use by VMS process
        /// </summary>
        private bool IsPortInUseByVMS(int port)
        {
            try
            {
                if (_vmsProcess == null || _vmsProcess.HasExited)
                    return false;

                // Simple check - if VMS process is running and port is in use, assume it's VMS
                return IsPortInUse(port);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Kill processes using the specified port
        /// </summary>
        private void KillProcessesUsingPort(int port)
        {
            try
            {
                Process netstatProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "netstat",
                        Arguments = "-ano",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        CreateNoWindow = true
                    }
                };

                netstatProcess.Start();
                string output = netstatProcess.StandardOutput.ReadToEnd();
                netstatProcess.WaitForExit();

                string[] lines = output.Split('\n');
                foreach (string line in lines)
                {
                    if (line.Contains($":{port} ") && line.Contains("LISTENING"))
                    {
                        string[] parts = line.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length > 4 && int.TryParse(parts[parts.Length - 1], out int pid))
                        {
                            try
                            {
                                Process conflictProcess = Process.GetProcessById(pid);
                                if (conflictProcess.ProcessName.ToLower() != "node" && 
                                    conflictProcess.ProcessName.ToLower() != "vms")
                                {
                                    LogMessage($"Killing process {conflictProcess.ProcessName} (PID: {pid}) using port {port}");
                                    conflictProcess.Kill();
                                }
                            }
                            catch (Exception ex)
                            {
                                LogMessage($"Could not kill process PID {pid}: {ex.Message}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Error killing processes using port {port}: {ex.Message}");
            }
        }

        /// <summary>
        /// Health check timer callback
        /// </summary>
        private void HealthCheck(object state)
        {
            if (_isShuttingDown) return;

            try
            {
                if (_vmsProcess == null || _vmsProcess.HasExited)
                {
                    LogMessage("VMS process not running - restarting...");
                    StartVMSServer();
                }
                else
                {
                    // Check if VMS is responding
                    if (!IsVMSResponding())
                    {
                        LogMessage("VMS not responding - restarting...");
                        StopVMSServer();
                        Thread.Sleep(5000); // Wait 5 seconds
                        StartVMSServer();
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Error in health check: {ex.Message}");
            }
        }

        /// <summary>
        /// Check if VMS is responding to HTTP requests
        /// </summary>
        private bool IsVMSResponding()
        {
            try
            {
                using (var client = new WebClient())
                {
                    client.DownloadString($"http://localhost:{_targetPort}/health");
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Check port reservation timer callback
        /// </summary>
        private void CheckPortReservation(object state)
        {
            if (_isShuttingDown) return;
            ReservePort(_targetPort);
        }

        /// <summary>
        /// Stop VMS server process
        /// </summary>
        private void StopVMSServer()
        {
            try
            {
                if (_vmsProcess != null && !_vmsProcess.HasExited)
                {
                    LogMessage("Stopping VMS server...");
                    _vmsProcess.Kill();
                    _vmsProcess.WaitForExit(10000); // Wait up to 10 seconds
                    LogMessage("VMS server stopped");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Error stopping VMS server: {ex.Message}");
            }
            finally
            {
                _vmsProcess?.Dispose();
                _vmsProcess = null;
            }
        }

        /// <summary>
        /// Service shutdown
        /// </summary>
        protected override void OnStop()
        {
            _isShuttingDown = true;
            LogMessage("VMS Service stopping...");

            try
            {
                // Stop timers
                _healthCheckTimer?.Dispose();
                _portReservationTimer?.Dispose();

                // Stop VMS server
                StopVMSServer();

                LogMessage("VMS Service stopped");
            }
            catch (Exception ex)
            {
                LogMessage($"Error stopping VMS Service: {ex.Message}");
            }
        }

        /// <summary>
        /// Log message to file
        /// </summary>
        private void LogMessage(string message)
        {
            try
            {
                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}";
                File.AppendAllText(_logPath, logEntry + Environment.NewLine);
                
                // Also log to Windows Event Log
                EventLog.WriteEntry(_serviceName, logEntry, EventLogEntryType.Information);
            }
            catch
            {
                // Ignore logging errors
            }
        }
    }
}

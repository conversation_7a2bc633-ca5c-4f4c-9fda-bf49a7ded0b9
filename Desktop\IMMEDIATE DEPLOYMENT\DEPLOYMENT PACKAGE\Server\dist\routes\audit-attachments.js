"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const multer_1 = __importDefault(require("multer"));
const fs_1 = __importDefault(require("fs"));
const auth_js_1 = require("../middleware/auth.js");
const db_js_1 = require("../database/db.js");
const audit_attachments_service_js_1 = require("../services/audit-attachments-service.js");
const logger_js_1 = require("../utils/logger.js");
const file_storage_js_1 = require("../utils/file-storage.js");
const router = express_1.default.Router();
router.use(auth_js_1.authenticate);
function requireAuditAccess(req, res, next) {
    if (req.user.department === 'AUDIT' || req.user.department === 'SYSTEM ADMIN') {
        next();
    }
    else {
        res.status(403).json({ error: 'Audit department access required' });
    }
}
router.use(requireAuditAccess);
const upload = (0, multer_1.default)({
    storage: multer_1.default.memoryStorage(),
    limits: {
        fileSize: file_storage_js_1.STORAGE_CONFIG.maxFileSize,
        files: 5
    },
    fileFilter: (req, file, cb) => {
        if (file_storage_js_1.STORAGE_CONFIG.allowedMimeTypes.includes(file.mimetype)) {
            cb(null, true);
        }
        else {
            cb(new Error('Only PDF and JPG files are allowed'));
        }
    }
});
router.post('/vouchers/:voucherId/attachments', upload.array('files', 5), async (req, res) => {
    try {
        const { voucherId } = req.params;
        const files = req.files;
        if (!files || files.length === 0) {
            return res.status(400).json({ error: 'No files provided' });
        }
        const vouchers = await (0, db_js_1.query)('SELECT claimant, description FROM vouchers WHERE id = ?', [voucherId]);
        if (vouchers.length === 0) {
            return res.status(404).json({ error: 'Voucher not found' });
        }
        const voucherData = vouchers[0];
        const uploadedAttachments = [];
        for (const file of files) {
            try {
                const attachment = await audit_attachments_service_js_1.AuditAttachmentsService.uploadAttachment(voucherId, file, req.user.id, voucherData);
                uploadedAttachments.push(attachment);
            }
            catch (fileError) {
                logger_js_1.logger.error(`Error uploading file ${file.originalname}:`, fileError);
            }
        }
        if (uploadedAttachments.length === 0) {
            return res.status(500).json({ error: 'Failed to upload any files' });
        }
        res.json({
            success: true,
            message: `Successfully uploaded ${uploadedAttachments.length} file(s)`,
            attachments: uploadedAttachments
        });
    }
    catch (error) {
        logger_js_1.logger.error('Error uploading audit attachments:', error);
        res.status(500).json({ error: 'Failed to upload attachments' });
    }
});
router.get('/vouchers/:voucherId/attachments', async (req, res) => {
    try {
        const { voucherId } = req.params;
        const attachments = await audit_attachments_service_js_1.AuditAttachmentsService.getVoucherAttachments(voucherId);
        res.json(attachments);
    }
    catch (error) {
        logger_js_1.logger.error('Error fetching voucher attachments:', error);
        res.status(500).json({ error: 'Failed to fetch attachments' });
    }
});
router.get('/attachments/:attachmentId/download', async (req, res) => {
    try {
        const { attachmentId } = req.params;
        const attachment = await audit_attachments_service_js_1.AuditAttachmentsService.getAttachmentById(attachmentId);
        if (!attachment) {
            return res.status(404).json({ error: 'Attachment not found' });
        }
        if (!fs_1.default.existsSync(attachment.file_path)) {
            return res.status(404).json({ error: 'File not found on disk' });
        }
        res.setHeader('Content-Disposition', `attachment; filename="${attachment.stored_filename}"`);
        res.setHeader('Content-Type', attachment.mime_type);
        res.setHeader('Content-Length', attachment.file_size);
        const fileStream = fs_1.default.createReadStream(attachment.file_path);
        fileStream.pipe(res);
    }
    catch (error) {
        logger_js_1.logger.error('Error downloading attachment:', error);
        res.status(500).json({ error: 'Failed to download attachment' });
    }
});
router.get('/attachments/:attachmentId/view', async (req, res) => {
    try {
        const { attachmentId } = req.params;
        const attachment = await audit_attachments_service_js_1.AuditAttachmentsService.getAttachmentById(attachmentId);
        if (!attachment) {
            return res.status(404).json({ error: 'Attachment not found' });
        }
        if (!fs_1.default.existsSync(attachment.file_path)) {
            return res.status(404).json({ error: 'File not found on disk' });
        }
        res.setHeader('Content-Type', attachment.mime_type);
        res.setHeader('Content-Length', attachment.file_size);
        res.setHeader('Content-Disposition', `inline; filename="${attachment.stored_filename}"`);
        const fileStream = fs_1.default.createReadStream(attachment.file_path);
        fileStream.pipe(res);
    }
    catch (error) {
        logger_js_1.logger.error('Error viewing attachment:', error);
        res.status(500).json({ error: 'Failed to view attachment' });
    }
});
router.delete('/attachments/:attachmentId', async (req, res) => {
    try {
        const { attachmentId } = req.params;
        const success = await audit_attachments_service_js_1.AuditAttachmentsService.deleteAttachment(attachmentId, req.user.name);
        if (success) {
            res.json({ success: true, message: 'Attachment deleted successfully' });
        }
        else {
            res.status(500).json({ error: 'Failed to delete attachment' });
        }
    }
    catch (error) {
        logger_js_1.logger.error('Error deleting attachment:', error);
        res.status(500).json({ error: 'Failed to delete attachment' });
    }
});
exports.default = router;

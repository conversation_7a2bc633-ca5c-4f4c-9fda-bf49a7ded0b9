"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.pendriveBackupRouter = void 0;
const express_1 = __importDefault(require("express"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const db_js_1 = require("../database/db.js");
const logger_js_1 = require("../utils/logger.js");
const auth_js_1 = require("../middleware/auth.js");
const pendriveBackupRouter = express_1.default.Router();
exports.pendriveBackupRouter = pendriveBackupRouter;
pendriveBackupRouter.post('/create-daily-backup', auth_js_1.authenticate, (0, auth_js_1.authorize)(['admin', 'ADMIN']), async (req, res) => {
    try {
        logger_js_1.logger.info('Creating end-of-day backup for pendrive...');
        const today = new Date();
        const dateStr = today.toISOString().split('T')[0];
        const backupDir = path_1.default.join(process.cwd(), 'daily-backups', dateStr);
        if (!fs_1.default.existsSync(backupDir)) {
            fs_1.default.mkdirSync(backupDir, { recursive: true });
        }
        const currentYear = new Date().getFullYear();
        const selectedYear = req.body.year || currentYear;
        const dbConfig = {
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT || '3306'),
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || 'vms@2025@1989',
            database: selectedYear === currentYear ?
                (process.env.DB_NAME || 'vms_production') :
                `vms_production_${selectedYear}`
        };
        const backupPackage = {
            metadata: {
                backupDate: today.toISOString(),
                backupType: 'END_OF_DAY',
                year: selectedYear,
                database: dbConfig.database,
                version: '3.0.0',
                createdBy: req.user?.name || 'Unknown'
            },
            files: []
        };
        const dbBackupFile = `database_${selectedYear}_${dateStr}.sql`;
        const dbBackupPath = path_1.default.join(backupDir, dbBackupFile);
        try {
            const mysql = require('mysql2/promise');
            const connection = await mysql.createConnection(dbConfig);
            const [tables] = await connection.execute('SHOW TABLES');
            const tableNames = tables.map((row) => Object.values(row)[0]);
            let backupContent = `-- VMS Production Database Backup (PENDRIVE)\n`;
            backupContent += `-- Generated: ${new Date().toISOString()}\n`;
            backupContent += `-- Database: ${dbConfig.database}\n`;
            backupContent += `-- Year: ${selectedYear}\n`;
            backupContent += `-- Type: End-of-Day Backup\n\n`;
            backupContent += `SET FOREIGN_KEY_CHECKS = 0;\n\n`;
            for (const tableName of tableNames) {
                const [createTable] = await connection.execute(`SHOW CREATE TABLE \`${tableName}\``);
                const createStatement = createTable[0]['Create Table'];
                backupContent += `-- Table: ${tableName}\n`;
                backupContent += `DROP TABLE IF EXISTS \`${tableName}\`;\n`;
                backupContent += `${createStatement};\n\n`;
                const [rows] = await connection.execute(`SELECT * FROM \`${tableName}\``);
                if (rows.length > 0) {
                    backupContent += `-- Data for table: ${tableName}\n`;
                    backupContent += `INSERT INTO \`${tableName}\` VALUES\n`;
                    const values = rows.map(row => {
                        const escapedValues = Object.values(row).map(value => {
                            if (value === null)
                                return 'NULL';
                            if (typeof value === 'string') {
                                if (value.includes('T') && value.includes('Z')) {
                                    const date = new Date(value);
                                    return `'${date.toISOString().slice(0, 19).replace('T', ' ')}'`;
                                }
                                return `'${value.replace(/'/g, "''")}'`;
                            }
                            if (value instanceof Date)
                                return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
                            return value;
                        });
                        return `(${escapedValues.join(', ')})`;
                    });
                    backupContent += values.join(',\n') + ';\n\n';
                }
            }
            backupContent += `SET FOREIGN_KEY_CHECKS = 1;\n`;
            fs_1.default.writeFileSync(dbBackupPath, backupContent, 'utf8');
            const stats = fs_1.default.statSync(dbBackupPath);
            if (stats.size === 0) {
                throw new Error('Backup file is empty');
            }
            await connection.end();
            backupPackage.files.push({
                name: dbBackupFile,
                type: 'database',
                size: stats.size,
                description: `Complete database backup for year ${selectedYear}`
            });
            logger_js_1.logger.info(`✅ Database backup created: ${dbBackupFile} (${stats.size} bytes)`);
        }
        catch (dbError) {
            logger_js_1.logger.error('❌ Database backup failed:', dbError);
            throw new Error('Database backup failed');
        }
        const settingsFile = `system_settings_${dateStr}.json`;
        const settingsPath = path_1.default.join(backupDir, settingsFile);
        const settings = await (0, db_js_1.query)('SELECT * FROM system_settings LIMIT 1');
        fs_1.default.writeFileSync(settingsPath, JSON.stringify(settings[0], null, 2));
        backupPackage.files.push({
            name: settingsFile,
            type: 'settings',
            size: fs_1.default.statSync(settingsPath).size,
            description: 'System configuration and settings'
        });
        const usersFile = `users_${dateStr}.json`;
        const usersPath = path_1.default.join(backupDir, usersFile);
        const users = await (0, db_js_1.query)('SELECT id, name, role, department, date_created, is_active FROM users');
        fs_1.default.writeFileSync(usersPath, JSON.stringify(users, null, 2));
        backupPackage.files.push({
            name: usersFile,
            type: 'users',
            size: fs_1.default.statSync(usersPath).size,
            description: 'User accounts and permissions'
        });
        const statsFile = `daily_stats_${dateStr}.json`;
        const statsPath = path_1.default.join(backupDir, statsFile);
        const stats = await (0, db_js_1.query)(`
      SELECT 
        COUNT(*) as total_vouchers,
        SUM(amount) as total_amount,
        COUNT(DISTINCT department) as active_departments,
        MIN(created_at) as earliest_voucher,
        MAX(created_at) as latest_voucher
      FROM vouchers 
      WHERE DATE(created_at) = ?
    `, [dateStr]);
        const dailyStats = {
            date: dateStr,
            year: selectedYear,
            statistics: stats[0],
            backup_info: {
                created_at: today.toISOString(),
                created_by: req.user?.name,
                backup_size: 0
            }
        };
        fs_1.default.writeFileSync(statsPath, JSON.stringify(dailyStats, null, 2));
        backupPackage.files.push({
            name: statsFile,
            type: 'statistics',
            size: fs_1.default.statSync(statsPath).size,
            description: 'Daily operational statistics'
        });
        const manifestFile = `backup_manifest_${dateStr}.json`;
        const manifestPath = path_1.default.join(backupDir, manifestFile);
        backupPackage.metadata.totalSize = backupPackage.files.reduce((sum, file) => sum + file.size, 0);
        fs_1.default.writeFileSync(manifestPath, JSON.stringify(backupPackage, null, 2));
        const readmeFile = `README_${dateStr}.txt`;
        const readmePath = path_1.default.join(backupDir, readmeFile);
        const readmeContent = `
VMS END-OF-DAY BACKUP - ${dateStr}
=====================================

Backup Information:
- Date: ${today.toLocaleDateString()}
- Time: ${today.toLocaleTimeString()}
- Year: ${selectedYear}
- Created by: ${req.user?.name}
- Database: ${dbConfig.database}

Files Included:
${backupPackage.files.map(f => `- ${f.name} (${f.description})`).join('\n')}

Restore Instructions:
1. Copy all files to VMS server
2. Use Admin Panel > Restore from Backup
3. Select the database backup file (.sql)
4. Follow on-screen instructions

IMPORTANT: Keep this backup in a secure location!
This backup contains sensitive financial data.

VMS Version: 3.0.0
Backup Type: END_OF_DAY
`;
        fs_1.default.writeFileSync(readmePath, readmeContent);
        await (0, db_js_1.query)('UPDATE system_settings SET last_backup_date = ? ORDER BY id LIMIT 1', [today.toISOString()]);
        await (0, db_js_1.query)(`INSERT INTO audit_logs (id, user_id, action, resource_type, resource_id, details, timestamp, ip_address)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [
            `backup-${Date.now()}`,
            req.user?.id,
            'CREATE_DAILY_BACKUP',
            'SYSTEM',
            'daily_backup',
            JSON.stringify({
                date: dateStr,
                year: selectedYear,
                files: backupPackage.files.length,
                totalSize: backupPackage.metadata.totalSize
            }),
            today.toISOString().slice(0, 19).replace('T', ' '),
            req.ip
        ]);
        res.json({
            success: true,
            message: 'End-of-day backup created successfully',
            backup: {
                date: dateStr,
                year: selectedYear,
                directory: backupDir,
                files: backupPackage.files,
                totalSize: backupPackage.metadata.totalSize,
                manifest: manifestFile
            },
            instructions: {
                pendrive: 'Copy the entire backup folder to your pendrive',
                location: `daily-backups/${dateStr}/`,
                files_count: backupPackage.files.length + 2
            }
        });
    }
    catch (error) {
        logger_js_1.logger.error('Daily backup creation failed:', error);
        res.status(500).json({
            error: 'Failed to create daily backup',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
pendriveBackupRouter.get('/daily-backups', (req, res) => {
    try {
        const dailyBackupsDir = path_1.default.join(process.cwd(), 'daily-backups');
        if (!fs_1.default.existsSync(dailyBackupsDir)) {
            return res.json([]);
        }
        const backupDirs = fs_1.default.readdirSync(dailyBackupsDir)
            .filter(dir => {
            const dirPath = path_1.default.join(dailyBackupsDir, dir);
            return fs_1.default.statSync(dirPath).isDirectory();
        })
            .map(dir => {
            const dirPath = path_1.default.join(dailyBackupsDir, dir);
            const manifestPath = path_1.default.join(dirPath, `backup_manifest_${dir}.json`);
            let manifest = null;
            if (fs_1.default.existsSync(manifestPath)) {
                try {
                    manifest = JSON.parse(fs_1.default.readFileSync(manifestPath, 'utf8'));
                }
                catch (e) {
                    logger_js_1.logger.warn(`Could not read manifest for ${dir}:`, e);
                }
            }
            return {
                date: dir,
                path: dirPath,
                manifest: manifest,
                files: fs_1.default.readdirSync(dirPath).length
            };
        })
            .sort((a, b) => b.date.localeCompare(a.date));
        res.json(backupDirs);
    }
    catch (error) {
        logger_js_1.logger.error('Error listing daily backups:', error);
        res.status(500).json({ error: 'Failed to list daily backups' });
    }
});

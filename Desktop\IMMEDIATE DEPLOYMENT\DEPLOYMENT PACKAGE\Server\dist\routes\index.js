"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.apiRouter = void 0;
const express_1 = __importDefault(require("express"));
const auth_js_1 = require("./auth.js");
const users_js_1 = require("./users.js");
const vouchers_js_1 = require("./vouchers.js");
const batches_js_1 = require("./batches.js");
const provisionalCash_js_1 = require("./provisionalCash.js");
const notifications_js_1 = require("./notifications.js");
const admin_js_1 = require("./admin.js");
const audit_js_1 = require("./audit.js");
const years_js_1 = require("./years.js");
const pendrive_backup_js_1 = require("./pendrive-backup.js");
const workflow_js_1 = __importDefault(require("./workflow.js"));
const password_change_requests_js_1 = __importDefault(require("./password-change-requests.js"));
const audit_attachments_js_1 = __importDefault(require("./audit-attachments.js"));
const login_updates_js_1 = require("./login-updates.js");
const auth_js_2 = require("../middleware/auth.js");
const apiRouter = express_1.default.Router();
exports.apiRouter = apiRouter;
apiRouter.use('/auth', auth_js_1.authRouter);
apiRouter.use('/users', users_js_1.userRouter);
apiRouter.use('/vouchers', vouchers_js_1.voucherRouter);
apiRouter.use('/batches', batches_js_1.batchRouter);
apiRouter.use('/provisional-cash', provisionalCash_js_1.provisionalCashRouter);
apiRouter.use('/notifications', notifications_js_1.notificationRouter);
apiRouter.use('/admin', admin_js_1.adminRouter);
apiRouter.use('/audit', audit_js_1.auditRouter);
apiRouter.use('/years', years_js_1.yearRouter);
apiRouter.use('/pendrive-backup', pendrive_backup_js_1.pendriveBackupRouter);
apiRouter.use('/workflow', workflow_js_1.default);
apiRouter.use('/password-change-requests', password_change_requests_js_1.default);
apiRouter.use('/audit', audit_attachments_js_1.default);
apiRouter.use('/login-updates', login_updates_js_1.loginUpdatesRouter);
apiRouter.get('/', (req, res) => {
    res.json({
        name: 'Voucher Management System API',
        version: '1.0.0',
        status: 'active'
    });
});
apiRouter.get('/health', (req, res) => {
    const healthData = {
        status: 'healthy',
        service: 'vms-server',
        serviceName: 'VMS-Server',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: '3.0.0',
        environment: process.env.NODE_ENV || 'production',
        pid: process.pid
    };
    res.json(healthData);
});
apiRouter.get('/system-info', auth_js_2.authenticate, async (req, res) => {
    try {
        const { query } = await Promise.resolve().then(() => __importStar(require('../database/db.js')));
        const activeUsers = await query(`
      SELECT COUNT(DISTINCT user_id) as count
      FROM active_sessions
      WHERE is_active = TRUE
      AND last_activity >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
    `);
        const totalSessions = await query(`
      SELECT COUNT(*) as count
      FROM active_sessions
      WHERE is_active = TRUE
    `);
        const systemInfo = {
            activeUsers: parseInt(activeUsers[0]?.count || '0'),
            totalSessions: parseInt(totalSessions[0]?.count || '0'),
            serverTime: new Date().toISOString(),
            uptime: process.uptime(),
            status: 'healthy'
        };
        res.json(systemInfo);
    }
    catch (error) {
        const { logger } = await Promise.resolve().then(() => __importStar(require('../utils/logger.js')));
        logger.error('System info error:', error);
        res.status(500).json({ error: 'Failed to get system info' });
    }
});
apiRouter.get('/basic-analytics', auth_js_2.authenticate, async (req, res) => {
    try {
        const { query } = await Promise.resolve().then(() => __importStar(require('../database/db.js')));
        const timeframe = req.query.timeframe || 'week';
        let dateCondition = '';
        switch (timeframe) {
            case 'today':
                dateCondition = 'DATE(created_at) = CURDATE()';
                break;
            case 'week':
                dateCondition = 'created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                break;
            case 'month':
                dateCondition = 'created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                break;
            default:
                dateCondition = 'created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        }
        const voucherMetrics = await query(`
      SELECT
        COUNT(*) as total_vouchers,
        SUM(CASE WHEN status = 'PENDING_DISPATCH' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'DISPATCHED' THEN 1 ELSE 0 END) as dispatched,
        SUM(CASE WHEN status = 'CERTIFIED' THEN 1 ELSE 0 END) as certified,
        SUM(amount) as total_amount,
        AVG(amount) as average_amount
      FROM vouchers
      WHERE ${dateCondition}
    `);
        const departmentActivity = await query(`
      SELECT
        department,
        COUNT(*) as voucher_count,
        SUM(amount) as total_amount
      FROM vouchers
      WHERE ${dateCondition}
      GROUP BY department
      ORDER BY voucher_count DESC
    `);
        const dailyActivity = await query(`
      SELECT
        DATE(created_at) as date,
        COUNT(*) as voucher_count,
        SUM(amount) as total_amount
      FROM vouchers
      WHERE ${dateCondition}
      GROUP BY DATE(created_at)
      ORDER BY date
    `);
        const analytics = {
            voucherMetrics: voucherMetrics[0] || {
                total_vouchers: 0,
                pending: 0,
                dispatched: 0,
                certified: 0,
                total_amount: 0,
                average_amount: 0
            },
            departmentActivity,
            dailyActivity,
            timeframe,
            generatedAt: new Date().toISOString()
        };
        res.json(analytics);
    }
    catch (error) {
        const { logger } = await Promise.resolve().then(() => __importStar(require('../utils/logger.js')));
        logger.error('Basic analytics error:', error);
        res.status(500).json({ error: 'Failed to get analytics data' });
    }
});

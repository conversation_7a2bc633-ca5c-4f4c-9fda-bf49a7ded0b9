import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  // PERFORMANCE OPTIMIZATION: Enhanced cache busting and build optimization
  build: {
    outDir: '../Server/public',
    emptyOutDir: true,
    rollupOptions: {
      output: {
        // Generate unique filenames with timestamps for cache busting
        entryFileNames: `assets/[name]-[hash]-${Date.now()}.js`,
        chunkFileNames: `assets/[name]-[hash]-${Date.now()}.js`,
        assetFileNames: `assets/[name]-[hash]-${Date.now()}.[ext]`,
        // Code splitting for better performance
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-select', '@radix-ui/react-checkbox'],
          utils: ['date-fns', 'clsx', 'tailwind-merge']
        }
      }
    },
    // Optimize for production
    minify: 'terser',
    sourcemap: false,
    target: 'es2020', // Updated for better performance
    // Reduce bundle size warnings threshold
    chunkSizeWarningLimit: 1000
  },
}));

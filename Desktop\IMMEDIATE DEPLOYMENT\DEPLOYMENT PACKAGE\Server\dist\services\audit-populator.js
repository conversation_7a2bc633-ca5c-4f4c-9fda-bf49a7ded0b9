"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditPopulator = void 0;
const db_js_1 = require("../database/db.js");
const logger_js_1 = require("../utils/logger.js");
const uuid_1 = require("uuid");
class AuditPopulator {
    static async populateAuditLogs() {
        try {
            logger_js_1.logger.info('🔍 Starting audit log population...');
            await Promise.all([
                this.populateLoginActivities(),
                this.populateVoucherActivities(),
                this.populateBatchActivities(),
                this.populateSystemActivities(),
                this.populateUserManagementActivities()
            ]);
            logger_js_1.logger.info('✅ Audit log population completed');
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error populating audit logs:', error);
        }
    }
    static async populateLoginActivities() {
        try {
            const sessions = await (0, db_js_1.query)(`
        SELECT DISTINCT 
          s.user_id, s.user_name, s.department, s.session_start, s.client_ip, s.user_agent,
          s.is_active, s.session_end
        FROM active_sessions s
        LEFT JOIN audit_logs a ON (
          a.user_id = s.user_id 
          AND a.action = 'LOGIN' 
          AND DATE(a.timestamp) = DATE(s.session_start)
        )
        WHERE a.id IS NULL
        AND s.session_start >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY s.session_start DESC
        LIMIT 50
      `);
            for (const session of sessions) {
                await this.createAuditLog({
                    user_id: session.user_id,
                    user_name: session.user_name,
                    department: session.department,
                    action: 'LOGIN',
                    description: `User logged into the system`,
                    resource_type: 'SESSION',
                    resource_id: session.user_id,
                    ip_address: session.client_ip,
                    user_agent: session.user_agent,
                    severity: 'INFO',
                    timestamp: session.session_start
                });
                if (!session.is_active && session.session_end) {
                    await this.createAuditLog({
                        user_id: session.user_id,
                        user_name: session.user_name,
                        department: session.department,
                        action: 'LOGOUT',
                        description: `User logged out of the system`,
                        resource_type: 'SESSION',
                        resource_id: session.user_id,
                        ip_address: session.client_ip,
                        user_agent: session.user_agent,
                        severity: 'INFO',
                        timestamp: session.session_end
                    });
                }
            }
            logger_js_1.logger.info(`📝 Populated ${sessions.length} login activities`);
        }
        catch (error) {
            logger_js_1.logger.error('Error populating login activities:', error);
        }
    }
    static async populateVoucherActivities() {
        try {
            const vouchers = await (0, db_js_1.query)(`
        SELECT 
          v.id, v.voucher_id, v.created_by, v.department, v.status,
          v.created_at, v.dispatch_time, v.certified_by, v.rejected_by,
          u1.name as creator_name,
          u2.name as certifier_name,
          u3.name as rejector_name
        FROM vouchers v
        LEFT JOIN users u1 ON v.created_by = u1.name
        LEFT JOIN users u2 ON v.certified_by = u2.name  
        LEFT JOIN users u3 ON v.rejected_by = u3.name
        WHERE v.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY v.created_at DESC
        LIMIT 100
      `);
            for (const voucher of vouchers) {
                if (voucher.created_by) {
                    await this.createAuditLog({
                        user_id: voucher.created_by,
                        user_name: voucher.creator_name || voucher.created_by,
                        department: voucher.department,
                        action: 'VOUCHER_CREATED',
                        description: `Created voucher ${voucher.voucher_id}`,
                        resource_type: 'VOUCHER',
                        resource_id: voucher.voucher_id,
                        severity: 'INFO',
                        timestamp: voucher.created_at
                    });
                }
                if (voucher.dispatch_time) {
                    await this.createAuditLog({
                        user_id: voucher.created_by,
                        user_name: voucher.creator_name || voucher.created_by,
                        department: voucher.department,
                        action: 'VOUCHER_DISPATCHED',
                        description: `Dispatched voucher ${voucher.voucher_id} to audit`,
                        resource_type: 'VOUCHER',
                        resource_id: voucher.voucher_id,
                        severity: 'INFO',
                        timestamp: voucher.dispatch_time
                    });
                }
                if (voucher.certified_by && voucher.status === 'VOUCHER CERTIFIED') {
                    await this.createAuditLog({
                        user_id: voucher.certified_by,
                        user_name: voucher.certifier_name || voucher.certified_by,
                        department: 'AUDIT',
                        action: 'VOUCHER_CERTIFIED',
                        description: `Certified voucher ${voucher.voucher_id}`,
                        resource_type: 'VOUCHER',
                        resource_id: voucher.voucher_id,
                        severity: 'INFO',
                        timestamp: new Date()
                    });
                }
                if (voucher.rejected_by && voucher.status === 'VOUCHER REJECTED') {
                    await this.createAuditLog({
                        user_id: voucher.rejected_by,
                        user_name: voucher.rejector_name || voucher.rejected_by,
                        department: 'AUDIT',
                        action: 'VOUCHER_REJECTED',
                        description: `Rejected voucher ${voucher.voucher_id}`,
                        resource_type: 'VOUCHER',
                        resource_id: voucher.voucher_id,
                        severity: 'WARNING',
                        timestamp: new Date()
                    });
                }
            }
            logger_js_1.logger.info(`📝 Populated voucher activities for ${vouchers.length} vouchers`);
        }
        catch (error) {
            logger_js_1.logger.error('Error populating voucher activities:', error);
        }
    }
    static async populateBatchActivities() {
        try {
            const batches = await (0, db_js_1.query)(`
        SELECT 
          b.id, b.batch_name, b.created_by, b.created_at, b.received_by, b.receipt_time,
          u1.name as creator_name,
          u2.name as receiver_name
        FROM voucher_batches b
        LEFT JOIN users u1 ON b.created_by = u1.name
        LEFT JOIN users u2 ON b.received_by = u2.name
        WHERE b.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY b.created_at DESC
        LIMIT 50
      `);
            for (const batch of batches) {
                if (batch.created_by) {
                    await this.createAuditLog({
                        user_id: batch.created_by,
                        user_name: batch.creator_name || batch.created_by,
                        department: 'FINANCE',
                        action: 'BATCH_CREATED',
                        description: `Created batch ${batch.batch_name}`,
                        resource_type: 'BATCH',
                        resource_id: batch.batch_name,
                        severity: 'INFO',
                        timestamp: batch.created_at
                    });
                }
                if (batch.received_by && batch.receipt_time) {
                    await this.createAuditLog({
                        user_id: batch.received_by,
                        user_name: batch.receiver_name || batch.received_by,
                        department: 'AUDIT',
                        action: 'BATCH_RECEIVED',
                        description: `Received batch ${batch.batch_name}`,
                        resource_type: 'BATCH',
                        resource_id: batch.batch_name,
                        severity: 'INFO',
                        timestamp: batch.receipt_time
                    });
                }
            }
            logger_js_1.logger.info(`📝 Populated batch activities for ${batches.length} batches`);
        }
        catch (error) {
            logger_js_1.logger.error('Error populating batch activities:', error);
        }
    }
    static async populateSystemActivities() {
        try {
            const existingStartup = await (0, db_js_1.query)(`
        SELECT id FROM audit_logs 
        WHERE action = 'SYSTEM_STARTUP' 
        AND DATE(timestamp) = CURDATE()
        LIMIT 1
      `);
            if (existingStartup.length === 0) {
                await this.createAuditLog({
                    user_id: 'SYSTEM',
                    user_name: 'SYSTEM',
                    department: 'SYSTEM',
                    action: 'SYSTEM_STARTUP',
                    description: 'VMS system started successfully',
                    resource_type: 'SYSTEM',
                    resource_id: 'VMS',
                    severity: 'INFO',
                    timestamp: new Date()
                });
            }
            logger_js_1.logger.info('📝 Populated system activities');
        }
        catch (error) {
            logger_js_1.logger.error('Error populating system activities:', error);
        }
    }
    static async populateUserManagementActivities() {
        try {
            const recentUsers = await (0, db_js_1.query)(`
        SELECT id, name, department, created_at
        FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY created_at DESC
        LIMIT 20
      `);
            for (const user of recentUsers) {
                await this.createAuditLog({
                    user_id: 'ADMIN',
                    user_name: 'SYSTEM ADMIN',
                    department: 'ADMIN',
                    action: 'USER_REGISTERED',
                    description: `New user registered: ${user.name} (${user.department})`,
                    resource_type: 'USER',
                    resource_id: user.id,
                    severity: 'INFO',
                    timestamp: user.created_at
                });
            }
            logger_js_1.logger.info(`📝 Populated user management activities for ${recentUsers.length} users`);
        }
        catch (error) {
            logger_js_1.logger.error('Error populating user management activities:', error);
        }
    }
    static async createAuditLog(logData) {
        try {
            const existing = await (0, db_js_1.query)(`
        SELECT id FROM audit_logs 
        WHERE user_id = ? AND action = ? AND resource_id = ? 
        AND DATE(timestamp) = DATE(?)
        LIMIT 1
      `, [
                logData.user_id,
                logData.action,
                logData.resource_id,
                logData.timestamp || new Date()
            ]);
            if (existing.length > 0) {
                return;
            }
            const id = (0, uuid_1.v4)();
            await (0, db_js_1.query)(`
        INSERT INTO audit_logs (
          id, timestamp, user_id, user_name, department, action, description,
          resource_type, resource_id, ip_address, user_agent, severity
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
                id,
                logData.timestamp || new Date(),
                logData.user_id,
                logData.user_name,
                logData.department,
                logData.action,
                logData.description,
                logData.resource_type,
                logData.resource_id,
                logData.ip_address || null,
                logData.user_agent || null,
                logData.severity || 'INFO'
            ]);
        }
        catch (error) {
            logger_js_1.logger.debug('Error creating audit log:', error);
        }
    }
    static async cleanupOldLogs() {
        try {
            const result = await (0, db_js_1.query)(`
        DELETE FROM audit_logs 
        WHERE timestamp < DATE_SUB(NOW(), INTERVAL 30 DAY)
      `);
            logger_js_1.logger.info(`🧹 Cleaned up ${result.affectedRows} old audit logs`);
        }
        catch (error) {
            logger_js_1.logger.error('Error cleaning up old audit logs:', error);
        }
    }
}
exports.AuditPopulator = AuditPopulator;

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deduplicateUserSessions = deduplicateUserSessions;
exports.getSessionStats = getSessionStats;
exports.cleanupUserSessions = cleanupUserSessions;
const db_js_1 = require("../database/db.js");
const logger_js_1 = require("./logger.js");
async function deduplicateUserSessions() {
    try {
        logger_js_1.logger.info('🔄 Starting session deduplication process...');
        const duplicateUsers = await (0, db_js_1.query)(`
      SELECT user_id, user_name, COUNT(*) as session_count
      FROM active_sessions 
      WHERE is_active = TRUE 
      GROUP BY user_id, user_name 
      HAVING COUNT(*) > 1
      ORDER BY session_count DESC
    `);
        if (duplicateUsers.length === 0) {
            logger_js_1.logger.info('✅ No duplicate sessions found');
            return;
        }
        logger_js_1.logger.info(`🔍 Found ${duplicateUsers.length} users with duplicate sessions`);
        let totalCleaned = 0;
        for (const user of duplicateUsers) {
            const userSessions = await (0, db_js_1.query)(`
        SELECT id, session_start, last_activity
        FROM active_sessions 
        WHERE user_id = ? AND is_active = TRUE 
        ORDER BY last_activity DESC, session_start DESC
      `, [user.user_id]);
            if (userSessions.length > 1) {
                const sessionsToDeactivate = userSessions.slice(1);
                const sessionIds = sessionsToDeactivate.map(s => s.id);
                await (0, db_js_1.query)(`
          UPDATE active_sessions 
          SET is_active = FALSE, session_end = NOW() 
          WHERE id IN (${sessionIds.map(() => '?').join(',')})
        `, sessionIds);
                totalCleaned += sessionsToDeactivate.length;
                logger_js_1.logger.info(`🧹 Cleaned ${sessionsToDeactivate.length} duplicate sessions for user ${user.user_name}`);
            }
        }
        logger_js_1.logger.info(`✅ Session deduplication complete: cleaned ${totalCleaned} duplicate sessions`);
    }
    catch (error) {
        logger_js_1.logger.error('❌ Session deduplication failed:', error);
        throw error;
    }
}
async function getSessionStats() {
    try {
        const [totalResult] = await (0, db_js_1.query)('SELECT COUNT(*) as count FROM active_sessions');
        const [activeResult] = await (0, db_js_1.query)('SELECT COUNT(*) as count FROM active_sessions WHERE is_active = TRUE');
        const [uniqueResult] = await (0, db_js_1.query)(`
      SELECT COUNT(DISTINCT user_id) as count 
      FROM active_sessions 
      WHERE is_active = TRUE
    `);
        const duplicateUsers = await (0, db_js_1.query)(`
      SELECT COUNT(*) as count
      FROM (
        SELECT user_id
        FROM active_sessions 
        WHERE is_active = TRUE 
        GROUP BY user_id 
        HAVING COUNT(*) > 1
      ) as duplicates
    `);
        return {
            totalSessions: totalResult.count,
            activeSessions: activeResult.count,
            uniqueActiveUsers: uniqueResult.count,
            duplicateUsers: duplicateUsers[0]?.count || 0
        };
    }
    catch (error) {
        logger_js_1.logger.error('Failed to get session stats:', error);
        throw error;
    }
}
async function cleanupUserSessions(userId, keepMostRecent = true) {
    try {
        if (keepMostRecent) {
            const userSessions = await (0, db_js_1.query)(`
        SELECT id, session_start, last_activity
        FROM active_sessions 
        WHERE user_id = ? AND is_active = TRUE 
        ORDER BY last_activity DESC, session_start DESC
      `, [userId]);
            if (userSessions.length > 1) {
                const sessionsToDeactivate = userSessions.slice(1);
                const sessionIds = sessionsToDeactivate.map(s => s.id);
                await (0, db_js_1.query)(`
          UPDATE active_sessions 
          SET is_active = FALSE, session_end = NOW() 
          WHERE id IN (${sessionIds.map(() => '?').join(',')})
        `, sessionIds);
                logger_js_1.logger.info(`Cleaned ${sessionsToDeactivate.length} duplicate sessions for user ${userId}`);
                return sessionsToDeactivate.length;
            }
        }
        else {
            const result = await (0, db_js_1.query)(`
        UPDATE active_sessions 
        SET is_active = FALSE, session_end = NOW() 
        WHERE user_id = ? AND is_active = TRUE
      `, [userId]);
            logger_js_1.logger.info(`Deactivated all sessions for user ${userId}`);
            return result.affectedRows || 0;
        }
        return 0;
    }
    catch (error) {
        logger_js_1.logger.error(`Failed to cleanup sessions for user ${userId}:`, error);
        throw error;
    }
}

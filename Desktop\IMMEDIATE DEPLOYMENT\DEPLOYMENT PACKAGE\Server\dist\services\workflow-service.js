"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeWorkflowService = initializeWorkflowService;
exports.getWorkflowManager = getWorkflowManager;
exports.checkWorkflowServiceHealth = checkWorkflowServiceHealth;
const VoucherWorkflowManager_1 = require("../workflow/VoucherWorkflowManager");
const logger_1 = require("../utils/logger");
let workflowManager = null;
async function initializeWorkflowService(db, eventPublisher) {
    try {
        logger_1.logger.info('Initializing workflow service...');
        const auditLogger = {
            log: async (data, tx) => {
                const query = `
          INSERT INTO workflow_audit_log 
          (id, voucher_id, from_state, to_state, event_type, user_id, timestamp, copy_id, metadata)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
                const values = [
                    generateId(),
                    data.voucherId,
                    data.fromState,
                    data.toState,
                    data.event,
                    data.userId,
                    data.timestamp,
                    data.copyId || null,
                    JSON.stringify(data.metadata || {})
                ];
                if (tx) {
                    await tx.execute(query, values);
                }
                else {
                    await db.execute(query, values);
                }
            }
        };
        const workflowEventPublisher = {
            publish: async (eventType, eventData) => {
                try {
                    if (eventPublisher && eventPublisher.emit) {
                        eventPublisher.emit(eventType, eventData);
                    }
                    logger_1.logger.info(`Workflow event published: ${eventType}`, {
                        voucherId: eventData.voucherId,
                        newState: eventData.newState,
                        affectedDepartments: eventData.affectedDepartments
                    });
                }
                catch (error) {
                    logger_1.logger.error('Error publishing workflow event:', error);
                }
            }
        };
        workflowManager = new VoucherWorkflowManager_1.VoucherWorkflowManager(db, workflowEventPublisher, auditLogger);
        logger_1.logger.info('✅ Workflow service initialized successfully');
        return workflowManager;
    }
    catch (error) {
        logger_1.logger.error('❌ Failed to initialize workflow service:', error);
        throw error;
    }
}
function getWorkflowManager() {
    if (!workflowManager) {
        throw new Error('Workflow service not initialized. Call initializeWorkflowService first.');
    }
    return workflowManager;
}
function generateId() {
    return require('uuid').v4();
}
async function checkWorkflowServiceHealth() {
    try {
        if (!workflowManager) {
            return {
                status: 'unhealthy',
                details: { error: 'Workflow manager not initialized' }
            };
        }
        const testResult = {
            workflowManagerInitialized: !!workflowManager,
            timestamp: new Date().toISOString()
        };
        return {
            status: 'healthy',
            details: testResult
        };
    }
    catch (error) {
        return {
            status: 'unhealthy',
            details: { error: error instanceof Error ? error.message : 'Unknown error' }
        };
    }
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cleanupStaleSessions = cleanupStaleSessions;
exports.scheduleSessionCleanup = scheduleSessionCleanup;
const db_js_1 = require("../database/db.js");
const logger_js_1 = require("../utils/logger.js");
const session_deduplication_js_1 = require("../utils/session-deduplication.js");
async function cleanupStaleSessions() {
    try {
        const INACTIVITY_THRESHOLD = 60;
        const OLD_SESSION_THRESHOLD = 24 * 60;
        const staleResult = await (0, db_js_1.query)(`UPDATE active_sessions
       SET is_active = FALSE, session_end = NOW()
       WHERE is_active = TRUE
       AND TIMESTAMPDIFF(MINUTE, last_activity, NOW()) >= ?`, [INACTIVITY_THRESHOLD]);
        if (staleResult.affectedRows > 0) {
            logger_js_1.logger.info(`Marked ${staleResult.affectedRows} sessions as inactive due to 60+ minutes inactivity`);
        }
        const deleteResult = await (0, db_js_1.query)(`DELETE FROM active_sessions
       WHERE is_active = FALSE
       AND TIMESTAMPDIFF(MINUTE, session_end, NOW()) >= ?`, [OLD_SESSION_THRESHOLD]);
        if (deleteResult.affectedRows > 0) {
            logger_js_1.logger.info(`Deleted ${deleteResult.affectedRows} old inactive sessions (24+ hours old)`);
        }
        await (0, session_deduplication_js_1.deduplicateUserSessions)();
        const stats = await (0, session_deduplication_js_1.getSessionStats)();
        logger_js_1.logger.debug(`Session stats: ${stats.activeSessions} active, ${stats.totalSessions - stats.activeSessions} inactive, ${stats.totalSessions} total`);
        if (stats.duplicateUsers > 0) {
            logger_js_1.logger.warn(`⚠️  Still found ${stats.duplicateUsers} users with duplicate sessions after cleanup`);
        }
        else {
            logger_js_1.logger.info(`✅ All users have unique sessions (${stats.uniqueActiveUsers} unique active users)`);
        }
    }
    catch (error) {
        logger_js_1.logger.error('Error cleaning up stale sessions:', error);
    }
}
function scheduleSessionCleanup() {
    cleanupStaleSessions();
    setInterval(cleanupStaleSessions, 60 * 1000);
    logger_js_1.logger.info('Session cleanup task scheduled');
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.webSocketRetryManager = exports.apiRetryManager = exports.databaseRetryManager = exports.retryManager = exports.WebSocketRetryManager = exports.APIRetryManager = exports.DatabaseRetryManager = exports.RetryManager = void 0;
class RetryManager {
    defaultOptions = {
        maxAttempts: 3,
        baseDelay: 1000,
        maxDelay: 30000,
        backoffFactor: 2,
        jitter: true,
        retryableErrors: [
            'ECONNRESET',
            'ENOTFOUND',
            'ECONNREFUSED',
            'ETIMEDOUT',
            'PROTOCOL_CONNECTION_LOST',
            'ER_LOCK_WAIT_TIMEOUT',
            'ER_LOCK_DEADLOCK'
        ]
    };
    async execute(fn, options = {}) {
        const opts = { ...this.defaultOptions, ...options };
        const startTime = Date.now();
        let lastError;
        for (let attempt = 1; attempt <= opts.maxAttempts; attempt++) {
            try {
                const result = await fn();
                return result;
            }
            catch (error) {
                lastError = error;
                if (!this.isRetryableError(error, opts.retryableErrors)) {
                    throw error;
                }
                if (attempt === opts.maxAttempts) {
                    break;
                }
                const delay = this.calculateDelay(attempt, opts);
                if (opts.onRetry) {
                    opts.onRetry(attempt, error);
                }
                await this.sleep(delay);
            }
        }
        throw lastError;
    }
    async executeWithResult(fn, options = {}) {
        const opts = { ...this.defaultOptions, ...options };
        const startTime = Date.now();
        let lastError;
        for (let attempt = 1; attempt <= opts.maxAttempts; attempt++) {
            try {
                const result = await fn();
                return {
                    success: true,
                    result,
                    attempts: attempt,
                    totalTime: Date.now() - startTime
                };
            }
            catch (error) {
                lastError = error;
                if (!this.isRetryableError(error, opts.retryableErrors) || attempt === opts.maxAttempts) {
                    break;
                }
                const delay = this.calculateDelay(attempt, opts);
                if (opts.onRetry) {
                    opts.onRetry(attempt, error);
                }
                await this.sleep(delay);
            }
        }
        return {
            success: false,
            error: lastError,
            attempts: opts.maxAttempts,
            totalTime: Date.now() - startTime
        };
    }
    isRetryableError(error, retryableErrors) {
        if (!retryableErrors || retryableErrors.length === 0) {
            return true;
        }
        return retryableErrors.some(retryableError => error.message?.includes(retryableError) ||
            error.code === retryableError ||
            error.name === retryableError ||
            error.errno === retryableError);
    }
    calculateDelay(attempt, options) {
        let delay = options.baseDelay * Math.pow(options.backoffFactor, attempt - 1);
        delay = Math.min(delay, options.maxDelay);
        if (options.jitter) {
            const jitterRange = delay * 0.25;
            const jitter = (Math.random() - 0.5) * 2 * jitterRange;
            delay += jitter;
        }
        return Math.max(0, Math.floor(delay));
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    wrap(fn, options = {}) {
        const retryManager = this;
        return (async (...args) => {
            return retryManager.execute(() => fn(...args), options);
        });
    }
}
exports.RetryManager = RetryManager;
class DatabaseRetryManager extends RetryManager {
    constructor() {
        super();
        Object.assign(this.defaultOptions, {
            maxAttempts: 5,
            baseDelay: 500,
            maxDelay: 10000,
            retryableErrors: [
                'PROTOCOL_CONNECTION_LOST',
                'ECONNRESET',
                'ENOTFOUND',
                'ECONNREFUSED',
                'ETIMEDOUT',
                'ER_LOCK_WAIT_TIMEOUT',
                'ER_LOCK_DEADLOCK',
                'ER_QUERY_INTERRUPTED'
            ]
        });
    }
}
exports.DatabaseRetryManager = DatabaseRetryManager;
class APIRetryManager extends RetryManager {
    constructor() {
        super();
        Object.assign(this.defaultOptions, {
            maxAttempts: 3,
            baseDelay: 1000,
            maxDelay: 15000,
            retryableErrors: [
                'ECONNRESET',
                'ENOTFOUND',
                'ECONNREFUSED',
                'ETIMEDOUT',
                'NETWORK_ERROR',
                'TIMEOUT'
            ]
        });
    }
}
exports.APIRetryManager = APIRetryManager;
class WebSocketRetryManager extends RetryManager {
    constructor() {
        super();
        Object.assign(this.defaultOptions, {
            maxAttempts: 10,
            baseDelay: 1000,
            maxDelay: 30000,
            backoffFactor: 1.5,
            retryableErrors: [
                'ECONNRESET',
                'ENOTFOUND',
                'ECONNREFUSED',
                'ETIMEDOUT',
                'WS_CONNECTION_FAILED',
                'WS_DISCONNECTED'
            ]
        });
    }
}
exports.WebSocketRetryManager = WebSocketRetryManager;
exports.retryManager = new RetryManager();
exports.databaseRetryManager = new DatabaseRetryManager();
exports.apiRetryManager = new APIRetryManager();
exports.webSocketRetryManager = new WebSocketRetryManager();

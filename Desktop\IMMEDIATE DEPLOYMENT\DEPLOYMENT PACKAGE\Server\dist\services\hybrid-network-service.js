"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.hybridNetworkService = exports.HybridNetworkService = void 0;
const events_1 = require("events");
const network_discovery_service_js_1 = require("./network-discovery-service.js");
const logger_js_1 = require("../utils/logger.js");
class HybridNetworkService extends events_1.EventEmitter {
    _config;
    _monitoringInterval = null;
    _isRunning = false;
    _healthCheckInterval = 30000;
    _networkChangeDetectionInterval = 10000;
    constructor(port = 8080) {
        super();
        this._config = {
            mode: 'hybrid',
            staticIP: null,
            dynamicIP: null,
            currentIP: 'localhost',
            port,
            isStaticAvailable: false,
            isDynamicAvailable: false,
            lastNetworkChange: new Date(),
            networkAdapter: null,
            subnetMask: null,
            gateway: null,
            dnsServers: []
        };
        logger_js_1.logger.info('🌐 Hybrid Network Service initialized');
    }
    async start() {
        try {
            if (this._isRunning) {
                logger_js_1.logger.warn('Hybrid Network Service is already running');
                return;
            }
            logger_js_1.logger.info('🚀 Starting Hybrid Network Management System...');
            await this.analyzeCurrentNetwork();
            await this.attemptStaticIPAssignment();
            await this.initializeNetworkDiscovery();
            this.startNetworkMonitoring();
            this._isRunning = true;
            logger_js_1.logger.info('✅ Hybrid Network Management System started successfully');
            logger_js_1.logger.info(`🖥️ Current Mode: ${this._config.mode}`);
            logger_js_1.logger.info(`📍 Active IP: ${this._config.currentIP}:${this._config.port}`);
            logger_js_1.logger.info(`🔧 Static IP Available: ${this._config.isStaticAvailable}`);
            logger_js_1.logger.info(`🔍 Dynamic Discovery Available: ${this._config.isDynamicAvailable}`);
        }
        catch (error) {
            logger_js_1.logger.error('❌ Failed to start Hybrid Network Service:', error);
            throw error;
        }
    }
    async stop() {
        try {
            if (!this._isRunning) {
                return;
            }
            logger_js_1.logger.info('🛑 Stopping Hybrid Network Management System...');
            if (this._monitoringInterval) {
                clearInterval(this._monitoringInterval);
                this._monitoringInterval = null;
            }
            await network_discovery_service_js_1.networkDiscoveryService.stop();
            this._isRunning = false;
            logger_js_1.logger.info('✅ Hybrid Network Management System stopped');
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error stopping Hybrid Network Service:', error);
        }
    }
    async analyzeCurrentNetwork() {
        try {
            logger_js_1.logger.info('🔍 Analyzing current network configuration...');
            const networkInfo = await this.getCurrentNetworkInfo();
            this._config.dynamicIP = networkInfo.currentIP;
            this._config.currentIP = networkInfo.currentIP;
            this._config.networkAdapter = networkInfo.adapterName;
            this._config.subnetMask = networkInfo.subnetMask;
            this._config.gateway = networkInfo.gateway;
            this._config.dnsServers = networkInfo.dnsServers;
            this._config.isDynamicAvailable = true;
            logger_js_1.logger.info(`📍 Current IP: ${networkInfo.currentIP}`);
            logger_js_1.logger.info(`🔌 Network Adapter: ${networkInfo.adapterName}`);
            logger_js_1.logger.info(`🌐 Gateway: ${networkInfo.gateway}`);
            logger_js_1.logger.info(`🔍 Subnet: ${networkInfo.subnetMask}`);
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error analyzing network:', error);
            throw error;
        }
    }
    async attemptStaticIPAssignment() {
        try {
            logger_js_1.logger.info('🔧 Attempting static IP assignment...');
            const staticIPConfig = await this.findOptimalStaticIP();
            if (!staticIPConfig) {
                logger_js_1.logger.warn('⚠️ No suitable static IP found - using dynamic mode');
                this._config.mode = 'dynamic';
                return;
            }
            const success = await this.assignStaticIP(staticIPConfig);
            if (success) {
                this._config.staticIP = staticIPConfig.targetIP;
                this._config.currentIP = staticIPConfig.targetIP;
                this._config.isStaticAvailable = true;
                this._config.mode = 'static';
                logger_js_1.logger.info(`✅ Static IP assigned successfully: ${staticIPConfig.targetIP}`);
            }
            else {
                logger_js_1.logger.warn('⚠️ Static IP assignment failed - falling back to dynamic mode');
                this._config.mode = 'dynamic';
            }
        }
        catch (error) {
            logger_js_1.logger.warn('⚠️ Static IP assignment error - using dynamic mode:', error);
            this._config.mode = 'dynamic';
        }
    }
    async initializeNetworkDiscovery() {
        try {
            logger_js_1.logger.info('🔍 Initializing network discovery backup system...');
            await network_discovery_service_js_1.networkDiscoveryService.start();
            this._config.isDynamicAvailable = true;
            logger_js_1.logger.info('✅ Network discovery backup system ready');
        }
        catch (error) {
            logger_js_1.logger.error('❌ Failed to initialize network discovery:', error);
            this._config.isDynamicAvailable = false;
        }
    }
    startNetworkMonitoring() {
        logger_js_1.logger.info('👁️ Starting continuous network monitoring...');
        this._monitoringInterval = setInterval(async () => {
            try {
                await this.performNetworkHealthCheck();
            }
            catch (error) {
                logger_js_1.logger.error('❌ Network health check error:', error);
            }
        }, this._healthCheckInterval);
        logger_js_1.logger.info(`⏰ Network monitoring active (${this._healthCheckInterval / 1000}s intervals)`);
    }
    async performNetworkHealthCheck() {
        try {
            const oldConfig = { ...this._config };
            let configChanged = false;
            if (this._config.staticIP) {
                const staticAvailable = await this.testIPConnectivity(this._config.staticIP);
                if (staticAvailable !== this._config.isStaticAvailable) {
                    this._config.isStaticAvailable = staticAvailable;
                    configChanged = true;
                    if (staticAvailable) {
                        logger_js_1.logger.info('✅ Static IP restored - switching back to static mode');
                        this._config.currentIP = this._config.staticIP;
                        this._config.mode = 'static';
                        this.emitNetworkChange('static_restored', oldConfig, this._config, 'Static IP connectivity restored');
                    }
                    else {
                        logger_js_1.logger.warn('⚠️ Static IP failed - switching to dynamic mode');
                        await this.switchToDynamicMode();
                        this.emitNetworkChange('static_failed', oldConfig, this._config, 'Static IP connectivity lost');
                    }
                }
            }
            const currentNetworkInfo = await this.getCurrentNetworkInfo();
            if (currentNetworkInfo.currentIP !== this._config.dynamicIP) {
                logger_js_1.logger.info(`🔄 Network change detected: ${this._config.dynamicIP} → ${currentNetworkInfo.currentIP}`);
                this._config.dynamicIP = currentNetworkInfo.currentIP;
                this._config.lastNetworkChange = new Date();
                configChanged = true;
                if (this._config.mode === 'dynamic') {
                    this._config.currentIP = currentNetworkInfo.currentIP;
                }
                this.emitNetworkChange('network_changed', oldConfig, this._config, 'Network configuration changed');
            }
            if (configChanged && this._config.isDynamicAvailable) {
                logger_js_1.logger.debug('🔄 Network discovery will adapt to changes automatically');
            }
        }
        catch (error) {
            logger_js_1.logger.error('❌ Network health check failed:', error);
        }
    }
    async switchToDynamicMode() {
        try {
            this._config.mode = 'dynamic';
            if (this._config.dynamicIP) {
                this._config.currentIP = this._config.dynamicIP;
            }
            else {
                const networkInfo = await this.getCurrentNetworkInfo();
                this._config.currentIP = networkInfo.currentIP;
                this._config.dynamicIP = networkInfo.currentIP;
            }
            logger_js_1.logger.info(`🔄 Switched to dynamic mode - using IP: ${this._config.currentIP}`);
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error switching to dynamic mode:', error);
        }
    }
    async testIPConnectivity(ip) {
        try {
            const { exec } = await Promise.resolve().then(() => __importStar(require('child_process')));
            const { promisify } = await Promise.resolve().then(() => __importStar(require('util')));
            const execAsync = promisify(exec);
            const pingCommand = process.platform === 'win32'
                ? `ping -n 1 -w 1000 ${ip}`
                : `ping -c 1 -W 1 ${ip}`;
            await execAsync(pingCommand);
            return true;
        }
        catch {
            return false;
        }
    }
    emitNetworkChange(type, oldConfig, newConfig, reason) {
        const event = {
            type,
            oldConfig,
            newConfig,
            timestamp: new Date(),
            reason
        };
        this.emit('networkChange', event);
        logger_js_1.logger.info(`📡 Network change event: ${type} - ${reason}`);
    }
    getNetworkConfiguration() {
        return { ...this._config };
    }
    getServerURL() {
        return `http://${this._config.currentIP}:${this._config.port}`;
    }
    isRunning() {
        return this._isRunning;
    }
    async switchToStaticMode() {
        if (!this._config.staticIP || !this._config.isStaticAvailable) {
            return false;
        }
        const oldConfig = { ...this._config };
        this._config.mode = 'static';
        this._config.currentIP = this._config.staticIP;
        this.emitNetworkChange('static_restored', oldConfig, this._config, 'Manual switch to static mode');
        return true;
    }
    async switchToDynamicModeManual() {
        const oldConfig = { ...this._config };
        await this.switchToDynamicMode();
        this.emitNetworkChange('dynamic_activated', oldConfig, this._config, 'Manual switch to dynamic mode');
        return true;
    }
    async getCurrentNetworkInfo() {
        const { staticIPAssignmentService } = await Promise.resolve().then(() => __importStar(require('./static-ip-assignment-service.js')));
        return await staticIPAssignmentService.getCurrentNetworkInfo();
    }
    async findOptimalStaticIP() {
        const { staticIPAssignmentService } = await Promise.resolve().then(() => __importStar(require('./static-ip-assignment-service.js')));
        return await staticIPAssignmentService.findOptimalStaticIP();
    }
    async assignStaticIP(config) {
        const { staticIPAssignmentService } = await Promise.resolve().then(() => __importStar(require('./static-ip-assignment-service.js')));
        const result = await staticIPAssignmentService.assignStaticIP(config);
        return result.success;
    }
}
exports.HybridNetworkService = HybridNetworkService;
exports.hybridNetworkService = new HybridNetworkService();

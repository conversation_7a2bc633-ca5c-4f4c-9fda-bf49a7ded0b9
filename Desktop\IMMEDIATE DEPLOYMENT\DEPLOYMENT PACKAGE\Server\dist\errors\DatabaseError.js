"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseError = void 0;
class DatabaseError extends Error {
    code;
    details;
    constructor(message, code = 'DATABASE_ERROR', details) {
        super(message);
        this.name = 'DatabaseError';
        this.code = code;
        this.details = details;
        Object.setPrototypeOf(this, DatabaseError.prototype);
    }
    toJSON() {
        return {
            name: this.name,
            message: this.message,
            code: this.code,
            details: this.details
        };
    }
}
exports.DatabaseError = DatabaseError;

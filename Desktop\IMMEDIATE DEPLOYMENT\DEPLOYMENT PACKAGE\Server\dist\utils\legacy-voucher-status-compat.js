"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VOUCHER_STATUSES = void 0;
exports.synchronizeVoucherFlags = synchronizeVoucherFlags;
exports.isValidStatusTransition = isValidStatusTransition;
exports.isValidStatusTransition_old = isValidStatusTransition_old;
exports.mapLegacyStatusToWorkflowState = mapLegacyStatusToWorkflowState;
exports.mapWorkflowStateToLegacyStatus = mapWorkflowStateToLegacyStatus;
exports.updateVoucherWithBothSystems = updateVoucherWithBothSystems;
exports.warnLegacyUsage = warnLegacyUsage;
const VoucherWorkflowStateMachine_1 = require("../workflow/VoucherWorkflowStateMachine");
exports.VOUCHER_STATUSES = {
    PENDING: 'PENDING SUBMISSION',
    PENDING_RECEIPT: 'PENDING RECEIPT',
    VOUCHER_PROCESSING: 'VOUCHER PROCESSING',
    AUDIT_PROCESSING: 'AUDIT: PROCESSING',
    VOUCHER_CERTIFIED: 'VOUCHER CERTIFIED',
    VOUCHER_REJECTED: 'VOUCHER REJECTED',
    VOUCHER_RETURNED: 'VOUCHER RETURNED',
    PENDING_DISPATCH: 'PENDING DISPATCH',
    DISPATCHED: 'DISPATCHED'
};
function synchronizeVoucherFlags(options) {
    const { status } = options;
    const flags = {
        isNew: status === exports.VOUCHER_STATUSES.PENDING,
        isPendingDispatch: status === exports.VOUCHER_STATUSES.PENDING_DISPATCH,
        isDispatched: status === exports.VOUCHER_STATUSES.DISPATCHED,
        isCertified: status === exports.VOUCHER_STATUSES.VOUCHER_CERTIFIED,
        isRejected: status === exports.VOUCHER_STATUSES.VOUCHER_REJECTED,
        isProcessing: status === exports.VOUCHER_STATUSES.VOUCHER_PROCESSING || status === exports.VOUCHER_STATUSES.AUDIT_PROCESSING
    };
    return { flags };
}
function isValidStatusTransition(fromStatus, toStatus, userRole, voucher) {
    console.warn('⚠️  Using legacy status transition validation - migrate to workflow state machine');
    const validTransitions = {
        [exports.VOUCHER_STATUSES.PENDING]: [exports.VOUCHER_STATUSES.PENDING_RECEIPT],
        [exports.VOUCHER_STATUSES.PENDING_RECEIPT]: [exports.VOUCHER_STATUSES.VOUCHER_PROCESSING],
        [exports.VOUCHER_STATUSES.VOUCHER_PROCESSING]: [exports.VOUCHER_STATUSES.VOUCHER_CERTIFIED, exports.VOUCHER_STATUSES.VOUCHER_REJECTED],
        [exports.VOUCHER_STATUSES.AUDIT_PROCESSING]: [exports.VOUCHER_STATUSES.PENDING_DISPATCH],
        [exports.VOUCHER_STATUSES.PENDING_DISPATCH]: [exports.VOUCHER_STATUSES.DISPATCHED],
        [exports.VOUCHER_STATUSES.VOUCHER_REJECTED]: [exports.VOUCHER_STATUSES.VOUCHER_PROCESSING],
    };
    const allowedTransitions = validTransitions[fromStatus] || [];
    const isValid = allowedTransitions.includes(toStatus);
    return {
        isValid,
        reason: isValid ? undefined : `Transition from ${fromStatus} to ${toStatus} not allowed`
    };
}
function isValidStatusTransition_old(fromStatus, toStatus, userRole, voucher) {
    const result = isValidStatusTransition(fromStatus, toStatus, userRole, voucher);
    return result.isValid;
}
function mapLegacyStatusToWorkflowState(legacyStatus) {
    const mapping = {
        [exports.VOUCHER_STATUSES.PENDING]: VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_PENDING,
        [exports.VOUCHER_STATUSES.PENDING_RECEIPT]: VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_PROCESSING,
        [exports.VOUCHER_STATUSES.VOUCHER_PROCESSING]: VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_NEW,
        [exports.VOUCHER_STATUSES.AUDIT_PROCESSING]: VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_PENDING_DISPATCH,
        [exports.VOUCHER_STATUSES.PENDING_DISPATCH]: VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_PENDING_DISPATCH,
        [exports.VOUCHER_STATUSES.DISPATCHED]: VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_DISPATCHED,
        [exports.VOUCHER_STATUSES.VOUCHER_CERTIFIED]: VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_CERTIFIED,
        [exports.VOUCHER_STATUSES.VOUCHER_REJECTED]: VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_REJECTED,
    };
    return mapping[legacyStatus] || VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_PENDING;
}
function mapWorkflowStateToLegacyStatus(workflowState) {
    const mapping = {
        [VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_PENDING]: exports.VOUCHER_STATUSES.PENDING,
        [VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_PROCESSING]: exports.VOUCHER_STATUSES.PENDING_RECEIPT,
        [VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_CERTIFIED]: exports.VOUCHER_STATUSES.VOUCHER_CERTIFIED,
        [VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_REJECTED]: exports.VOUCHER_STATUSES.VOUCHER_REJECTED,
        [VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_RETURNED]: exports.VOUCHER_STATUSES.VOUCHER_RETURNED,
        [VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_RESUBMISSION_RECEIVED]: 'RE-SUBMISSION',
        [VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_NEW]: exports.VOUCHER_STATUSES.VOUCHER_PROCESSING,
        [VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_NEW_RESUBMITTED]: exports.VOUCHER_STATUSES.VOUCHER_PROCESSING,
        [VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_PENDING_DISPATCH]: exports.VOUCHER_STATUSES.AUDIT_PROCESSING,
        [VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_PENDING_DISPATCH_REJECTED]: exports.VOUCHER_STATUSES.AUDIT_PROCESSING,
        [VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_PENDING_DISPATCH_RETURNED]: exports.VOUCHER_STATUSES.AUDIT_PROCESSING,
        [VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_DISPATCHED]: exports.VOUCHER_STATUSES.DISPATCHED,
        [VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_REJECTED_COPY]: exports.VOUCHER_STATUSES.VOUCHER_REJECTED,
        [VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_RETURNED_COPY]: exports.VOUCHER_STATUSES.VOUCHER_RETURNED,
    };
    return mapping[workflowState] || exports.VOUCHER_STATUSES.PENDING;
}
async function updateVoucherWithBothSystems(db, voucherId, workflowState, additionalFields = {}) {
    const legacyStatus = mapWorkflowStateToLegacyStatus(workflowState);
    const { flags } = synchronizeVoucherFlags({ status: legacyStatus });
    const updateFields = {
        status: legacyStatus,
        workflow_state: workflowState,
        flags: JSON.stringify(flags),
        last_modified: new Date(),
        ...additionalFields
    };
    const setClause = Object.keys(updateFields)
        .map(key => `${key} = ?`)
        .join(', ');
    const values = [...Object.values(updateFields), voucherId];
    await db.execute(`
    UPDATE vouchers 
    SET ${setClause}
    WHERE id = ?
  `, values);
    console.log(`✅ Updated voucher ${voucherId}: ${legacyStatus} (${workflowState})`);
}
function warnLegacyUsage(functionName, location) {
    console.warn(`⚠️  DEPRECATED: ${functionName} used in ${location}`);
    console.warn(`   Please migrate to workflow state machine`);
    console.warn(`   See: /workflow/VoucherWorkflowStateMachine.ts`);
}

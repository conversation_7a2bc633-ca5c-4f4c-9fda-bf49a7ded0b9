"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setIoInstance = setIoInstance;
exports.getIoInstance = getIoInstance;
exports.setupSocketHandlers = setupSocketHandlers;
exports.sendToUser = sendToUser;
exports.sendToDepartment = sendToDepartment;
exports.broadcast = broadcast;
exports.getConnectionStats = getConnectionStats;
const logger_js_1 = require("../utils/logger.js");
const connections = new Map();
let io = null;
function setIoInstance(ioInstance) {
    io = ioInstance;
    logger_js_1.logger.info('✅ Socket.IO instance set');
}
function getIoInstance() {
    return io;
}
function setupSocketHandlers(ioInstance) {
    io = ioInstance;
    io.on('connection', (socket) => {
        handleConnection(socket);
    });
    io.on('error', (error) => {
        logger_js_1.logger.error('Socket.IO server error:', error);
    });
    logger_js_1.logger.info('✅ Socket handlers initialized');
}
function handleConnection(socket) {
    const connectionId = socket.id;
    const now = Date.now();
    connections.set(connectionId, {
        id: connectionId,
        connectedAt: now,
        lastActivity: now
    });
    logger_js_1.logger.info(`🔗 WebSocket connected: ${connectionId}`);
    socket.on('authenticate', (data) => {
        try {
            const { userId, userName, department, token } = data || {};
            if (!userId || !userName) {
                socket.emit('auth_error', { message: 'Missing required fields' });
                return;
            }
            const connection = connections.get(connectionId);
            if (connection) {
                connection.userId = userId;
                connection.userName = userName;
                connection.department = department;
                connection.lastActivity = Date.now();
            }
            if (department) {
                socket.join(department);
                logger_js_1.logger.info(`📂 Socket joined department: ${connectionId} → ${department}`);
            }
            socket.emit('authenticated', {
                success: true,
                connectionId,
                timestamp: Date.now()
            });
            logger_js_1.logger.info(`✅ Socket authenticated: ${connectionId} (${userName} - ${department})`);
        }
        catch (error) {
            logger_js_1.logger.error('Authentication error:', error);
            socket.emit('auth_error', { message: 'Authentication failed' });
        }
    });
    socket.on('join_department', (data) => {
        try {
            const { department } = data || {};
            if (!department) {
                socket.emit('join_error', { message: 'Department is required' });
                return;
            }
            socket.join(department);
            const connection = connections.get(connectionId);
            if (connection) {
                connection.department = department;
                connection.lastActivity = Date.now();
            }
            socket.emit('joined_department', {
                department,
                success: true,
                timestamp: Date.now()
            });
            logger_js_1.logger.info(`📂 Socket joined department: ${connectionId} → ${department}`);
        }
        catch (error) {
            logger_js_1.logger.error('Join department error:', error);
            socket.emit('join_error', { message: 'Failed to join department' });
        }
    });
    socket.on('ping', () => {
        try {
            socket.emit('pong', { timestamp: Date.now() });
            updateActivity(connectionId);
        }
        catch (error) {
            logger_js_1.logger.error('Ping error:', error);
        }
    });
    socket.on('broadcast_to_department', (data) => {
        try {
            const { department, event, payload } = data || {};
            if (!department || !event) {
                socket.emit('broadcast_error', { message: 'Department and event are required' });
                return;
            }
            io?.to(department).emit(event, payload);
            updateActivity(connectionId);
            logger_js_1.logger.debug(`📡 Broadcast to ${department}: ${event}`);
        }
        catch (error) {
            logger_js_1.logger.error('Broadcast error:', error);
            socket.emit('broadcast_error', { message: 'Broadcast failed' });
        }
    });
    socket.on('disconnect', (reason) => {
        handleDisconnection(connectionId, reason);
    });
    socket.on('error', (error) => {
        logger_js_1.logger.error(`Socket error for ${connectionId}:`, error);
    });
    socket.onAny(() => {
        updateActivity(connectionId);
    });
}
function updateActivity(connectionId) {
    const connection = connections.get(connectionId);
    if (connection) {
        connection.lastActivity = Date.now();
    }
}
function handleDisconnection(connectionId, reason) {
    const connection = connections.get(connectionId);
    if (connection) {
        connections.delete(connectionId);
        logger_js_1.logger.info(`🔌 WebSocket disconnected: ${connectionId} (${reason})`);
    }
}
function sendToUser(userId, event, data) {
    if (!io)
        return false;
    try {
        let sent = false;
        for (const [socketId, connection] of connections) {
            if (connection.userId === userId) {
                const socket = io.sockets.sockets.get(socketId);
                if (socket) {
                    socket.emit(event, data);
                    sent = true;
                }
            }
        }
        if (sent) {
            logger_js_1.logger.debug(`📤 Message sent to user ${userId}: ${event}`);
        }
        return sent;
    }
    catch (error) {
        logger_js_1.logger.error('Send to user error:', error);
        return false;
    }
}
function sendToDepartment(department, event, data) {
    if (!io)
        return false;
    try {
        io.to(department).emit(event, data);
        logger_js_1.logger.debug(`📤 Message sent to department ${department}: ${event}`);
        return true;
    }
    catch (error) {
        logger_js_1.logger.error('Send to department error:', error);
        return false;
    }
}
function broadcast(event, data) {
    if (!io)
        return false;
    try {
        io.emit(event, data);
        logger_js_1.logger.debug(`📡 Broadcast sent: ${event}`);
        return true;
    }
    catch (error) {
        logger_js_1.logger.error('Broadcast error:', error);
        return false;
    }
}
function getConnectionStats() {
    const stats = {
        total: connections.size,
        byDepartment: {},
        authenticated: 0
    };
    for (const connection of connections.values()) {
        if (connection.userId) {
            stats.authenticated++;
        }
        if (connection.department) {
            stats.byDepartment[connection.department] = (stats.byDepartment[connection.department] || 0) + 1;
        }
    }
    return stats;
}
setInterval(() => {
    const now = Date.now();
    const timeout = 5 * 60 * 1000;
    for (const [socketId, connection] of connections) {
        if (now - connection.lastActivity > timeout) {
            const socket = io?.sockets.sockets.get(socketId);
            if (socket) {
                socket.disconnect(true);
            }
            connections.delete(socketId);
            logger_js_1.logger.info(`🧹 Cleaned up inactive connection: ${socketId}`);
        }
    }
}, 60000);

#!/usr/bin/env node

/**
 * VMS Admin Server - Dedicated Admin Interface Server
 * Runs on port 8081 as designed for VMS-ADMIN.exe
 */

const express = require('express');
const path = require('path');
const cors = require('cors');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();
const ADMIN_PORT = 8081;
const MAIN_SERVER_PORT = 8080;
const MAIN_SERVER_URL = `http://localhost:${MAIN_SERVER_PORT}`;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Logging middleware
app.use((req, res, next) => {
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
    next();
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'VMS Admin Server',
        port: ADMIN_PORT,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        mainServer: MAIN_SERVER_URL
    });
});

// Serve admin interface files
app.use('/admin', express.static(path.join(__dirname, 'public')));

// Serve admin dashboard at root
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'admin.html'));
});

// Admin login endpoint
app.post('/admin-login', async (req, res) => {
    try {
        const { username, password } = req.body;

        // Forward login to main server
        const response = await fetch(`${MAIN_SERVER_URL}/api/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        if (response.ok) {
            const data = await response.json();
            // Check if user has admin role
            if (data.user && data.user.role === 'admin') {
                res.json({
                    success: true,
                    token: data.token,
                    user: data.user,
                    message: 'Admin login successful'
                });
            } else {
                res.status(403).json({
                    success: false,
                    message: 'Admin access required'
                });
            }
        } else {
            const error = await response.json();
            res.status(response.status).json({
                success: false,
                message: error.message || 'Login failed'
            });
        }
    } catch (error) {
        console.error('Admin login error:', error);
        res.status(500).json({
            success: false,
            message: 'Login service unavailable'
        });
    }
});

// Proxy API requests to main server
app.use('/api', createProxyMiddleware({
    target: MAIN_SERVER_URL,
    changeOrigin: true,
    onError: (err, req, res) => {
        console.error('Proxy error:', err.message);
        res.status(502).json({
            error: 'Main VMS server unavailable',
            message: 'Please ensure the main VMS server is running on port 8080',
            timestamp: new Date().toISOString()
        });
    },
    onProxyReq: (proxyReq, req, res) => {
        console.log(`Proxying: ${req.method} ${req.url} -> ${MAIN_SERVER_URL}${req.url}`);
    }
}));

// Fallback route - serve admin interface
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'admin.html'));
});

// Error handling
app.use((err, req, res, next) => {
    console.error('Server error:', err);
    res.status(500).json({
        error: 'Internal server error',
        message: err.message,
        timestamp: new Date().toISOString()
    });
});

// Start server
async function startAdminServer() {
    try {
        // Check if main server is running
        const http = require('http');
        const checkMainServer = () => {
            return new Promise((resolve) => {
                const req = http.get(`${MAIN_SERVER_URL}/health`, (res) => {
                    resolve(res.statusCode === 200);
                });
                req.on('error', () => resolve(false));
                req.setTimeout(5000, () => {
                    req.destroy();
                    resolve(false);
                });
            });
        };

        const mainServerRunning = await checkMainServer();
        if (!mainServerRunning) {
            console.warn(`⚠️ Warning: Main VMS server not detected at ${MAIN_SERVER_URL}`);
            console.warn('   Admin interface will still start, but API calls may fail');
        } else {
            console.log(`✅ Main VMS server detected at ${MAIN_SERVER_URL}`);
        }

        const server = app.listen(ADMIN_PORT, '0.0.0.0', () => {
            console.log('');
            console.log('🛡️ VMS Admin Server Started');
            console.log('============================');
            console.log(`📍 Admin Interface: http://localhost:${ADMIN_PORT}`);
            console.log(`🔗 Main Server: ${MAIN_SERVER_URL}`);
            console.log(`🕐 Started: ${new Date().toLocaleString()}`);
            console.log('');
            console.log('Admin Interface Features:');
            console.log('• System monitoring and management');
            console.log('• Database backup operations');
            console.log('• User and audit log management');
            console.log('• Real-time server status');
            console.log('• Configuration management');
            console.log('');
            console.log('🎯 VMS-ADMIN.exe will connect to this server');
        });

        // Graceful shutdown
        process.on('SIGINT', () => {
            console.log('\n🛑 Shutting down VMS Admin Server...');
            server.close(() => {
                console.log('✅ VMS Admin Server stopped');
                process.exit(0);
            });
        });

        process.on('SIGTERM', () => {
            console.log('\n🛑 Shutting down VMS Admin Server...');
            server.close(() => {
                console.log('✅ VMS Admin Server stopped');
                process.exit(0);
            });
        });

        // Handle server errors
        server.on('error', (error) => {
            if (error.code === 'EADDRINUSE') {
                console.error(`❌ Port ${ADMIN_PORT} is already in use`);
                console.error('   Another VMS Admin Server may already be running');
                console.error('   Or another service is using port 8081');
                process.exit(1);
            } else {
                console.error('❌ Server error:', error);
                process.exit(1);
            }
        });

    } catch (error) {
        console.error('❌ Failed to start VMS Admin Server:', error);
        process.exit(1);
    }
}

// Auto-install dependencies if needed
async function ensureDependencies() {
    const requiredPackages = ['express', 'cors', 'http-proxy-middleware'];
    const fs = require('fs');
    const { execSync } = require('child_process');
    
    try {
        // Check if package.json exists
        const packageJsonPath = path.join(__dirname, 'package.json');
        if (fs.existsSync(packageJsonPath)) {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
            
            const missingPackages = requiredPackages.filter(pkg => !dependencies[pkg]);
            
            if (missingPackages.length > 0) {
                console.log(`📦 Installing missing packages: ${missingPackages.join(', ')}`);
                execSync(`npm install ${missingPackages.join(' ')}`, { stdio: 'inherit' });
            }
        }
    } catch (error) {
        console.warn('⚠️ Could not check/install dependencies:', error.message);
        console.warn('   Please ensure express, cors, and http-proxy-middleware are installed');
    }
}

// Initialize and start
async function init() {
    console.log('🚀 Initializing VMS Admin Server...');
    
    try {
        await ensureDependencies();
        await startAdminServer();
    } catch (error) {
        console.error('❌ Initialization failed:', error);
        process.exit(1);
    }
}

// Start if run directly
if (require.main === module) {
    init();
}

module.exports = { app, startAdminServer };

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.networkIntegrationService = exports.NetworkIntegrationService = void 0;
const events_1 = require("events");
const hybrid_network_service_1 = require("./hybrid-network-service");
const intelligent_fallback_service_1 = require("./intelligent-fallback-service");
const self_healing_network_monitor_1 = require("./self-healing-network-monitor");
const network_discovery_service_1 = require("./network-discovery-service");
const portable_deployment_service_1 = require("./portable-deployment-service");
const logger_1 = require("../utils/logger");
class NetworkIntegrationService extends events_1.EventEmitter {
    _isInitialized = false;
    _isRunning = false;
    _startupTime = null;
    constructor() {
        super();
        logger_1.logger.info('🌐 Network Integration Service initialized');
    }
    async initialize() {
        try {
            if (this._isInitialized) {
                logger_1.logger.warn('Network Integration Service already initialized');
                return;
            }
            logger_1.logger.info('🚀 Initializing Network Integration System...');
            const startTime = Date.now();
            logger_1.logger.info('📁 Phase 1: Initializing portable deployment...');
            await portable_deployment_service_1.portableDeploymentService.initialize();
            logger_1.logger.info('🌐 Phase 2: Starting hybrid network management...');
            await hybrid_network_service_1.hybridNetworkService.start();
            logger_1.logger.info('🧠 Phase 3: Starting intelligent fallback system...');
            await intelligent_fallback_service_1.intelligentFallbackService.start();
            logger_1.logger.info('🏥 Phase 4: Starting self-healing monitor...');
            await self_healing_network_monitor_1.selfHealingNetworkMonitor.start();
            this.setupEventListeners();
            const initTime = Date.now() - startTime;
            this._isInitialized = true;
            this._isRunning = true;
            this._startupTime = new Date();
            logger_1.logger.info('✅ Network Integration System initialized successfully');
            logger_1.logger.info(`⏱️ Initialization completed in ${initTime}ms`);
            const status = await this.getSystemStatus();
            logger_1.logger.info(`🎯 System Status: ${status.overall}`);
            logger_1.logger.info(`🔗 Server URL: ${status.serverURL}`);
            logger_1.logger.info(`⚙️ Network Mode: ${status.currentMode}`);
            this.emit('initialized', status);
        }
        catch (error) {
            logger_1.logger.error('❌ Failed to initialize Network Integration System:', error);
            throw error;
        }
    }
    async shutdown() {
        try {
            if (!this._isRunning) {
                return;
            }
            logger_1.logger.info('🛑 Shutting down Network Integration System...');
            await self_healing_network_monitor_1.selfHealingNetworkMonitor.stop();
            await intelligent_fallback_service_1.intelligentFallbackService.stop();
            await hybrid_network_service_1.hybridNetworkService.stop();
            this._isRunning = false;
            logger_1.logger.info('✅ Network Integration System shutdown complete');
        }
        catch (error) {
            logger_1.logger.error('❌ Error during Network Integration System shutdown:', error);
        }
    }
    setupEventListeners() {
        hybrid_network_service_1.hybridNetworkService.on('networkChange', (event) => {
            logger_1.logger.info(`🔄 Network change detected: ${event.type}`);
            this.emit('networkChange', event);
        });
        intelligent_fallback_service_1.intelligentFallbackService.on('fallbackDecision', (decision) => {
            logger_1.logger.info(`🎯 Fallback decision: ${decision.action} (${decision.confidence}% confidence)`);
            this.emit('fallbackDecision', decision);
        });
        self_healing_network_monitor_1.selfHealingNetworkMonitor.on('healingAction', (action) => {
            logger_1.logger.info(`🏥 Healing action: ${action.description} - ${action.success ? 'Success' : 'Failed'}`);
            this.emit('healingAction', action);
        });
        self_healing_network_monitor_1.selfHealingNetworkMonitor.on('healthCheck', (result) => {
            logger_1.logger.debug(`💓 Health check completed in ${result.duration}ms`);
            this.emit('healthCheck', result);
        });
        logger_1.logger.info('📡 Event listeners configured');
    }
    async getSystemStatus() {
        try {
            const hybridConfig = hybrid_network_service_1.hybridNetworkService.getNetworkConfiguration();
            const issues = [];
            const recommendations = [];
            const components = {
                hybridNetwork: hybrid_network_service_1.hybridNetworkService.isRunning(),
                intelligentFallback: intelligent_fallback_service_1.intelligentFallbackService.isRunning(),
                selfHealing: self_healing_network_monitor_1.selfHealingNetworkMonitor.isRunning(),
                networkDiscovery: network_discovery_service_1.networkDiscoveryService.isServiceRunning(),
                portableDeployment: portable_deployment_service_1.portableDeploymentService.isInitialized()
            };
            let overall = 'healthy';
            if (!components.hybridNetwork) {
                issues.push('Hybrid network service not running');
                overall = 'critical';
            }
            if (!components.networkDiscovery) {
                issues.push('Network discovery service not active');
                if (overall !== 'critical')
                    overall = 'degraded';
            }
            if (!components.intelligentFallback) {
                issues.push('Intelligent fallback system not active');
                recommendations.push('Restart fallback system for optimal performance');
            }
            if (!components.selfHealing) {
                issues.push('Self-healing monitor not active');
                recommendations.push('Enable self-healing for proactive network management');
            }
            if (!hybridConfig.isStaticAvailable && !hybridConfig.isDynamicAvailable) {
                issues.push('No network connectivity available');
                overall = 'offline';
            }
            else if (!hybridConfig.isStaticAvailable) {
                issues.push('Static IP not available');
                recommendations.push('Check static IP configuration');
                if (overall === 'healthy')
                    overall = 'degraded';
            }
            if (hybridConfig.mode === 'dynamic' && hybridConfig.isStaticAvailable) {
                recommendations.push('Consider switching to static mode for better performance');
            }
            return {
                overall,
                components,
                currentMode: hybridConfig.mode,
                serverURL: hybrid_network_service_1.hybridNetworkService.getServerURL(),
                lastUpdate: new Date(),
                issues,
                recommendations
            };
        }
        catch (error) {
            logger_1.logger.error('❌ Error getting system status:', error);
            return {
                overall: 'critical',
                components: {
                    hybridNetwork: false,
                    intelligentFallback: false,
                    selfHealing: false,
                    networkDiscovery: false,
                    portableDeployment: false
                },
                currentMode: 'dynamic',
                serverURL: 'http://localhost:8080',
                lastUpdate: new Date(),
                issues: ['System status check failed'],
                recommendations: ['Restart network services']
            };
        }
    }
    async getSystemStats() {
        try {
            return {
                uptime: this._startupTime ? Date.now() - this._startupTime.getTime() : 0,
                initialized: this._isInitialized,
                running: this._isRunning,
                networkConfig: hybrid_network_service_1.hybridNetworkService.getNetworkConfiguration(),
                fallbackStats: intelligent_fallback_service_1.intelligentFallbackService.getStats(),
                healingStats: self_healing_network_monitor_1.selfHealingNetworkMonitor.getStats(),
                discoveryStats: network_discovery_service_1.networkDiscoveryService.getNetworkStats(),
                deploymentConfig: portable_deployment_service_1.portableDeploymentService.getDeploymentConfig()
            };
        }
        catch (error) {
            logger_1.logger.error('❌ Error getting system stats:', error);
            return null;
        }
    }
    async switchNetworkMode(mode) {
        try {
            logger_1.logger.info(`🔄 Manually switching to ${mode} mode...`);
            let success = false;
            if (mode === 'static') {
                success = await hybrid_network_service_1.hybridNetworkService.switchToStaticMode();
            }
            else {
                success = await hybrid_network_service_1.hybridNetworkService.switchToDynamicModeManual();
            }
            if (success) {
                logger_1.logger.info(`✅ Successfully switched to ${mode} mode`);
                this.emit('modeSwitch', { mode, success: true });
            }
            else {
                logger_1.logger.warn(`⚠️ Failed to switch to ${mode} mode`);
                this.emit('modeSwitch', { mode, success: false });
            }
            return success;
        }
        catch (error) {
            logger_1.logger.error(`❌ Error switching to ${mode} mode:`, error);
            return false;
        }
    }
    async triggerHealing() {
        try {
            logger_1.logger.info('🏥 Triggering manual healing...');
            await self_healing_network_monitor_1.selfHealingNetworkMonitor.performHealthCheck();
            return true;
        }
        catch (error) {
            logger_1.logger.error('❌ Error triggering manual healing:', error);
            return false;
        }
    }
    async restartServices() {
        try {
            logger_1.logger.info('🔄 Restarting all network services...');
            await this.shutdown();
            await new Promise(resolve => setTimeout(resolve, 2000));
            await this.initialize();
            logger_1.logger.info('✅ All network services restarted successfully');
            return true;
        }
        catch (error) {
            logger_1.logger.error('❌ Error restarting network services:', error);
            return false;
        }
    }
    getServerURL() {
        return hybrid_network_service_1.hybridNetworkService.getServerURL();
    }
    isReady() {
        return this._isInitialized && this._isRunning;
    }
    getDeploymentInfo() {
        const deploymentConfig = portable_deployment_service_1.portableDeploymentService.getDeploymentConfig();
        const networkConfig = hybrid_network_service_1.hybridNetworkService.getNetworkConfiguration();
        return {
            deploymentId: deploymentConfig?.deploymentId,
            deploymentTime: deploymentConfig?.deploymentTime,
            deploymentMode: deploymentConfig?.deploymentMode,
            vmsPath: deploymentConfig?.paths.vmsRoot,
            serverIP: networkConfig.currentIP,
            serverPort: networkConfig.port,
            networkMode: networkConfig.mode,
            staticIPAvailable: networkConfig.isStaticAvailable,
            dynamicIPAvailable: networkConfig.isDynamicAvailable
        };
    }
    async exportConfiguration() {
        try {
            const status = await this.getSystemStatus();
            const stats = await this.getSystemStats();
            const deploymentInfo = this.getDeploymentInfo();
            return {
                exportTime: new Date(),
                version: '1.0.0',
                status,
                stats,
                deploymentInfo,
                configuration: {
                    hybrid: hybrid_network_service_1.hybridNetworkService.getNetworkConfiguration(),
                    fallback: intelligent_fallback_service_1.intelligentFallbackService.getStats(),
                    healing: self_healing_network_monitor_1.selfHealingNetworkMonitor.getStats()
                }
            };
        }
        catch (error) {
            logger_1.logger.error('❌ Error exporting configuration:', error);
            return null;
        }
    }
}
exports.NetworkIntegrationService = NetworkIntegrationService;
exports.networkIntegrationService = new NetworkIntegrationService();

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.circuitBreakerManager = exports.CircuitBreakerManager = exports.CircuitBreaker = exports.CircuitState = void 0;
var CircuitState;
(function (CircuitState) {
    CircuitState["CLOSED"] = "CLOSED";
    CircuitState["OPEN"] = "OPEN";
    CircuitState["HALF_OPEN"] = "HALF_OPEN";
})(CircuitState || (exports.CircuitState = CircuitState = {}));
class CircuitBreaker {
    state = CircuitState.CLOSED;
    failureCount = 0;
    successCount = 0;
    lastFailureTime = null;
    nextAttemptTime = null;
    options;
    constructor(options = {}) {
        this.options = {
            failureThreshold: options.failureThreshold || 5,
            recoveryTimeout: options.recoveryTimeout || 60000,
            monitoringPeriod: options.monitoringPeriod || 120000,
            expectedErrors: options.expectedErrors || []
        };
    }
    async execute(fn) {
        if (this.state === CircuitState.OPEN) {
            if (this.shouldAttemptReset()) {
                this.state = CircuitState.HALF_OPEN;
            }
            else {
                throw new Error(`Circuit breaker is OPEN. Next attempt at ${new Date(this.nextAttemptTime)}`);
            }
        }
        try {
            const result = await fn();
            this.onSuccess();
            return result;
        }
        catch (error) {
            this.onFailure(error);
            throw error;
        }
    }
    onSuccess() {
        this.successCount++;
        if (this.state === CircuitState.HALF_OPEN) {
            this.reset();
        }
    }
    onFailure(error) {
        if (this.shouldCountFailure(error)) {
            this.failureCount++;
            this.lastFailureTime = Date.now();
            if (this.state === CircuitState.HALF_OPEN) {
                this.open();
            }
            else if (this.failureCount >= this.options.failureThreshold) {
                this.open();
            }
        }
    }
    shouldCountFailure(error) {
        if (this.options.expectedErrors.length === 0) {
            return true;
        }
        return this.options.expectedErrors.some(expectedError => error.message?.includes(expectedError) ||
            error.code === expectedError ||
            error.name === expectedError);
    }
    open() {
        this.state = CircuitState.OPEN;
        this.nextAttemptTime = Date.now() + this.options.recoveryTimeout;
    }
    reset() {
        this.state = CircuitState.CLOSED;
        this.failureCount = 0;
        this.successCount = 0;
        this.lastFailureTime = null;
        this.nextAttemptTime = null;
    }
    shouldAttemptReset() {
        return this.nextAttemptTime !== null && Date.now() >= this.nextAttemptTime;
    }
    getStats() {
        return {
            state: this.state,
            failureCount: this.failureCount,
            successCount: this.successCount,
            lastFailureTime: this.lastFailureTime,
            nextAttemptTime: this.nextAttemptTime
        };
    }
    isHealthy() {
        return this.state === CircuitState.CLOSED;
    }
    forceOpen() {
        this.open();
    }
    forceClose() {
        this.reset();
    }
}
exports.CircuitBreaker = CircuitBreaker;
class CircuitBreakerManager {
    breakers = new Map();
    getBreaker(serviceName, options) {
        if (!this.breakers.has(serviceName)) {
            this.breakers.set(serviceName, new CircuitBreaker(options));
        }
        return this.breakers.get(serviceName);
    }
    async execute(serviceName, fn, options) {
        const breaker = this.getBreaker(serviceName, options);
        return breaker.execute(fn);
    }
    getHealthStatus() {
        const status = {};
        for (const [serviceName, breaker] of this.breakers) {
            status[serviceName] = breaker.getStats();
        }
        return status;
    }
    resetAll() {
        for (const breaker of this.breakers.values()) {
            breaker.forceClose();
        }
    }
    getUnhealthyServices() {
        const unhealthy = [];
        for (const [serviceName, breaker] of this.breakers) {
            if (!breaker.isHealthy()) {
                unhealthy.push(serviceName);
            }
        }
        return unhealthy;
    }
}
exports.CircuitBreakerManager = CircuitBreakerManager;
exports.circuitBreakerManager = new CircuitBreakerManager();

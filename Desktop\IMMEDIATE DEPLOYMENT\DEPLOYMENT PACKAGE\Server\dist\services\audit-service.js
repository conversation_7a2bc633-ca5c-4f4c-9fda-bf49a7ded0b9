"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditService = void 0;
const db_js_1 = require("../database/db.js");
const logger_js_1 = require("../utils/logger.js");
const uuid_1 = require("uuid");
class AuditService {
    static async ensureAuditTable() {
        try {
            await (0, db_js_1.query)(`
        CREATE TABLE IF NOT EXISTS audit_logs (
          id VARCHAR(36) PRIMARY KEY,
          timestamp DATETIME NOT NULL,
          user_id VARCHAR(36) NOT NULL,
          user_name VARCHAR(255) NOT NULL,
          department VARCHAR(50) NOT NULL,
          action VARCHAR(100) NOT NULL,
          description TEXT NOT NULL,
          resource_type VARCHAR(50) NOT NULL,
          resource_id VARCHAR(255),
          details JSON,
          ip_address VARCHAR(50),
          user_agent TEXT,
          severity ENUM('INFO', 'WARNING', 'ERROR') DEFAULT 'INFO',
          INDEX (timestamp),
          INDEX (user_id),
          INDEX (department),
          INDEX (action),
          INDEX (resource_type),
          INDEX (severity)
        )
      `);
        }
        catch (error) {
            logger_js_1.logger.error('Failed to ensure audit_logs table:', error);
        }
    }
    static async logActivity(params) {
        try {
            await this.ensureAuditTable();
            const mysqlTimestamp = new Date().toISOString().slice(0, 19).replace('T', ' ');
            const auditEntry = {
                id: (0, uuid_1.v4)(),
                timestamp: new Date().toISOString(),
                user: {
                    id: params.userId,
                    name: params.userName,
                    department: params.department
                },
                action: params.action,
                description: params.description,
                resourceType: params.resourceType,
                resourceId: params.resourceId,
                details: params.details || {},
                ipAddress: params.ipAddress,
                userAgent: params.userAgent,
                severity: params.severity || 'INFO'
            };
            await (0, db_js_1.query)(`INSERT INTO audit_logs (
          id, timestamp, user_id, user_name, department, action, description,
          resource_type, resource_id, details, ip_address, user_agent, severity
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, [
                auditEntry.id,
                mysqlTimestamp,
                auditEntry.user.id,
                auditEntry.user.name,
                auditEntry.user.department,
                auditEntry.action,
                auditEntry.description,
                auditEntry.resourceType,
                auditEntry.resourceId,
                JSON.stringify(auditEntry.details),
                auditEntry.ipAddress,
                auditEntry.userAgent,
                auditEntry.severity
            ]);
            logger_js_1.logger.info(`Audit logged: ${auditEntry.description}`);
            return auditEntry;
        }
        catch (error) {
            logger_js_1.logger.error('Failed to log audit entry:', error);
            throw error;
        }
    }
    static async getAuditLogs(filters = {}) {
        try {
            let whereClause = 'WHERE 1=1';
            const params = [];
            if (filters.startDate) {
                whereClause += ' AND timestamp >= ?';
                params.push(filters.startDate);
            }
            if (filters.endDate) {
                whereClause += ' AND timestamp <= ?';
                params.push(filters.endDate);
            }
            if (filters.userId) {
                whereClause += ' AND user_id = ?';
                params.push(filters.userId);
            }
            if (filters.department) {
                whereClause += ' AND department = ?';
                params.push(filters.department);
            }
            if (filters.action) {
                whereClause += ' AND action = ?';
                params.push(filters.action);
            }
            if (filters.severity) {
                whereClause += ' AND severity = ?';
                params.push(filters.severity);
            }
            const limit = filters.limit || 100;
            const offset = filters.offset || 0;
            const logs = await (0, db_js_1.query)(`SELECT * FROM audit_logs 
         ${whereClause} 
         ORDER BY timestamp DESC 
         LIMIT ? OFFSET ?`, [...params, limit, offset]);
            const parsedLogs = logs.map(log => ({
                ...log,
                details: JSON.parse(log.details || '{}')
            }));
            return parsedLogs;
        }
        catch (error) {
            logger_js_1.logger.error('Failed to get audit logs:', error);
            throw error;
        }
    }
    static async getAuditStats(timeframe = 'today') {
        try {
            let dateCondition = '';
            switch (timeframe) {
                case 'today':
                    dateCondition = 'DATE(timestamp) = CURDATE()';
                    break;
                case 'week':
                    dateCondition = 'timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                    break;
                case 'month':
                    dateCondition = 'timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                    break;
            }
            const stats = await (0, db_js_1.query)(`SELECT 
          COUNT(*) as total_activities,
          COUNT(DISTINCT user_id) as active_users,
          COUNT(CASE WHEN action = 'LOGIN' THEN 1 END) as logins,
          COUNT(CASE WHEN action = 'CREATE_VOUCHER' THEN 1 END) as vouchers_created,
          COUNT(CASE WHEN action = 'CERTIFY_VOUCHER' THEN 1 END) as vouchers_certified,
          COUNT(CASE WHEN severity = 'ERROR' THEN 1 END) as errors,
          COUNT(CASE WHEN severity = 'WARNING' THEN 1 END) as warnings
         FROM audit_logs 
         WHERE ${dateCondition}`, []);
            return stats[0] || {
                total_activities: 0,
                active_users: 0,
                logins: 0,
                vouchers_created: 0,
                vouchers_certified: 0,
                errors: 0,
                warnings: 0
            };
        }
        catch (error) {
            logger_js_1.logger.error('Failed to get audit stats:', error);
            throw error;
        }
    }
    static async getRecentActivities(limit = 20) {
        try {
            const activities = await (0, db_js_1.query)(`SELECT * FROM audit_logs 
         ORDER BY timestamp DESC 
         LIMIT ?`, [limit]);
            return activities.map(activity => ({
                ...activity,
                details: JSON.parse(activity.details || '{}')
            }));
        }
        catch (error) {
            logger_js_1.logger.error('Failed to get recent activities:', error);
            throw error;
        }
    }
    static async logLogin(userId, userName, department, ipAddress, userAgent) {
        return this.logActivity({
            userId,
            userName,
            department,
            action: 'LOGIN',
            description: `${userName} logged in to ${department} department`,
            resourceType: 'USER',
            resourceId: userId,
            ipAddress,
            userAgent,
            severity: 'INFO'
        });
    }
    static async logLogout(userId, userName, department, ipAddress, userAgent) {
        return this.logActivity({
            userId,
            userName,
            department,
            action: 'LOGOUT',
            description: `${userName} logged out from ${department} department`,
            resourceType: 'USER',
            resourceId: userId,
            ipAddress,
            userAgent,
            severity: 'INFO'
        });
    }
    static async logVoucherCreate(userId, userName, department, voucherId, amount, ipAddress, userAgent) {
        return this.logActivity({
            userId,
            userName,
            department,
            action: 'CREATE_VOUCHER',
            description: `${userName} created voucher #${voucherId} for GH₵ ${amount.toLocaleString()}`,
            resourceType: 'VOUCHER',
            resourceId: voucherId,
            details: { amount },
            ipAddress,
            userAgent,
            severity: 'INFO'
        });
    }
    static async logVoucherDispatch(userId, userName, department, voucherCount, targetDept, ipAddress, userAgent) {
        return this.logActivity({
            userId,
            userName,
            department,
            action: 'DISPATCH_VOUCHERS',
            description: `${userName} dispatched ${voucherCount} vouchers from ${department} to ${targetDept}`,
            resourceType: 'VOUCHER_BATCH',
            details: { voucherCount, targetDepartment: targetDept },
            ipAddress,
            userAgent,
            severity: 'INFO'
        });
    }
    static async logVoucherCertify(userId, userName, department, voucherId, ipAddress, userAgent) {
        return this.logActivity({
            userId,
            userName,
            department,
            action: 'CERTIFY_VOUCHER',
            description: `${userName} certified voucher #${voucherId} and returned to originating department`,
            resourceType: 'VOUCHER',
            resourceId: voucherId,
            ipAddress,
            userAgent,
            severity: 'INFO'
        });
    }
    static async logBackupCreate(userId, userName, department, backupType, ipAddress, userAgent) {
        return this.logActivity({
            userId,
            userName,
            department,
            action: 'CREATE_BACKUP',
            description: `${userName} created ${backupType} backup`,
            resourceType: 'SYSTEM',
            details: { backupType },
            ipAddress,
            userAgent,
            severity: 'INFO'
        });
    }
    static async logSystemError(userId, userName, department, error, ipAddress, userAgent) {
        return this.logActivity({
            userId,
            userName,
            department,
            action: 'SYSTEM_ERROR',
            description: `System error occurred for ${userName}: ${error}`,
            resourceType: 'SYSTEM',
            details: { error },
            ipAddress,
            userAgent,
            severity: 'ERROR'
        });
    }
}
exports.AuditService = AuditService;
exports.default = AuditService;

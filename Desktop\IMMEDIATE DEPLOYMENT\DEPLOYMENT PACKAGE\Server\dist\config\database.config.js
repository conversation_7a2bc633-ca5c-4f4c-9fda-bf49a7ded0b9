"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseOptimizations = exports.highPerformanceDatabaseConfig = exports.developmentDatabaseConfig = exports.productionDatabaseConfig = void 0;
exports.getDatabaseConfig = getDatabaseConfig;
exports.validateDatabaseConfig = validateDatabaseConfig;
exports.buildConnectionString = buildConnectionString;
exports.getConfigSummary = getConfigSummary;
exports.getDatabaseOptimizations = getDatabaseOptimizations;
exports.productionDatabaseConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'vms@2025@1989',
    database: process.env.DB_NAME || 'vms_production',
    connectionLimit: 50,
    queueLimit: 100,
    acquireTimeout: 30000,
    timeout: 60000,
    reconnectTimeout: 10000,
    maxReconnectAttempts: 10,
    reconnectDelay: 2000,
    healthCheckInterval: 30000,
    slowQueryThreshold: 5000,
    charset: 'utf8mb4',
    timezone: '+00:00'
};
exports.developmentDatabaseConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3307'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'vms@2025@1989',
    database: process.env.DB_NAME || 'vms_development',
    connectionLimit: 10,
    queueLimit: 20,
    acquireTimeout: 10000,
    timeout: 30000,
    reconnectTimeout: 5000,
    maxReconnectAttempts: 5,
    reconnectDelay: 1000,
    healthCheckInterval: 15000,
    slowQueryThreshold: 2000,
    charset: 'utf8mb4',
    timezone: '+00:00'
};
exports.highPerformanceDatabaseConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'vms@2025@1989',
    database: process.env.DB_NAME || 'vms_production',
    connectionLimit: 100,
    queueLimit: 200,
    acquireTimeout: 45000,
    timeout: 90000,
    reconnectTimeout: 15000,
    maxReconnectAttempts: 15,
    reconnectDelay: 1000,
    healthCheckInterval: 20000,
    slowQueryThreshold: 10000,
    charset: 'utf8mb4',
    timezone: '+00:00'
};
function getDatabaseConfig() {
    const environment = process.env.NODE_ENV || 'development';
    const performanceMode = process.env.DB_PERFORMANCE_MODE || 'standard';
    switch (environment) {
        case 'production':
            if (performanceMode === 'high') {
                return exports.highPerformanceDatabaseConfig;
            }
            return exports.productionDatabaseConfig;
        case 'development':
        case 'dev':
            return exports.developmentDatabaseConfig;
        case 'staging':
        case 'test':
            return {
                ...exports.productionDatabaseConfig,
                acquireTimeout: exports.developmentDatabaseConfig.acquireTimeout,
                timeout: exports.developmentDatabaseConfig.timeout,
                healthCheckInterval: exports.developmentDatabaseConfig.healthCheckInterval
            };
        default:
            return exports.developmentDatabaseConfig;
    }
}
function validateDatabaseConfig(config) {
    try {
        if (!config.host || !config.user || !config.database) {
            throw new Error('Missing required database configuration fields');
        }
        if (config.port < 1 || config.port > 65535) {
            throw new Error('Invalid database port number');
        }
        if (config.connectionLimit < 1 || config.connectionLimit > 1000) {
            throw new Error('Connection limit must be between 1 and 1000');
        }
        if (config.queueLimit < 0 || config.queueLimit > 10000) {
            throw new Error('Queue limit must be between 0 and 10000');
        }
        if (config.acquireTimeout < 1000 || config.acquireTimeout > 300000) {
            throw new Error('Acquire timeout must be between 1-300 seconds');
        }
        if (config.timeout < 1000 || config.timeout > 600000) {
            throw new Error('Query timeout must be between 1-600 seconds');
        }
        if (config.maxReconnectAttempts < 1 || config.maxReconnectAttempts > 100) {
            throw new Error('Max reconnect attempts must be between 1 and 100');
        }
        if (config.reconnectDelay < 100 || config.reconnectDelay > 60000) {
            throw new Error('Reconnect delay must be between 100ms and 60 seconds');
        }
        if (config.healthCheckInterval < 5000 || config.healthCheckInterval > 300000) {
            throw new Error('Health check interval must be between 5-300 seconds');
        }
        if (config.slowQueryThreshold < 100 || config.slowQueryThreshold > 300000) {
            throw new Error('Slow query threshold must be between 100ms and 300 seconds');
        }
        return true;
    }
    catch (error) {
        console.error('Database configuration validation failed:', error?.message || 'Unknown error');
        return false;
    }
}
function buildConnectionString(config) {
    return `mysql://${config.user}:${config.password}@${config.host}:${config.port}/${config.database}`;
}
function getConfigSummary(config) {
    return {
        host: config.host,
        port: config.port,
        database: config.database,
        connectionLimit: config.connectionLimit,
        queueLimit: config.queueLimit,
        acquireTimeout: config.acquireTimeout,
        timeout: config.timeout,
        healthCheckInterval: config.healthCheckInterval,
        slowQueryThreshold: config.slowQueryThreshold
    };
}
exports.databaseOptimizations = {
    production: {
        enableSSL: true,
        enableCompression: true,
        enableCaching: true,
        logSlowQueries: true,
        enableMetrics: true
    },
    development: {
        enableSSL: false,
        enableCompression: false,
        enableCaching: false,
        logSlowQueries: true,
        enableMetrics: true,
        verboseLogging: true
    },
    testing: {
        enableSSL: false,
        enableCompression: false,
        enableCaching: false,
        logSlowQueries: false,
        enableMetrics: false,
        fastFailover: true
    }
};
function getDatabaseOptimizations() {
    const environment = process.env.NODE_ENV || 'development';
    return exports.databaseOptimizations[environment] || exports.databaseOptimizations.development;
}

"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.hash = hash;
exports.compare = compare;
const crypto_1 = __importDefault(require("crypto"));
const ITERATIONS = 10000;
const KEY_LENGTH = 64;
const DIGEST = 'sha512';
const ENCODING = 'base64';
const SALT_LENGTH = 16;
function generateSalt() {
    return crypto_1.default.randomBytes(SALT_LENGTH).toString(ENCODING);
}
function hashWithSalt(password, salt) {
    const actualSalt = salt || generateSalt();
    const hash = crypto_1.default.pbkdf2Sync(password, actualSalt, ITERATIONS, KEY_LENGTH, DIGEST).toString(ENCODING);
    return `$pbkdf2$${ITERATIONS}$${actualSalt}$${hash}`;
}
async function hash(password, saltRounds) {
    return hashWithSalt(password);
}
async function compare(password, hashedPassword) {
    if (hashedPassword.startsWith('$2')) {
        console.warn('Legacy bcrypt hash detected, cannot verify without bcrypt module');
        return false;
    }
    try {
        const parts = hashedPassword.split('$');
        if (parts.length !== 5 || parts[1] !== 'pbkdf2') {
            return false;
        }
        const iterations = parseInt(parts[2], 10);
        const salt = parts[3];
        const storedHash = parts[4];
        const inputHash = crypto_1.default.pbkdf2Sync(password, salt, iterations, KEY_LENGTH, DIGEST).toString(ENCODING);
        return crypto_1.default.timingSafeEqual(Buffer.from(storedHash), Buffer.from(inputHash));
    }
    catch (error) {
        console.error('Error comparing passwords:', error);
        return false;
    }
}
exports.default = {
    hash,
    compare,
    hashSync: (password, saltRounds) => hashWithSalt(password),
    compareSync: (password, hashedPassword) => {
        try {
            if (hashedPassword.startsWith('$2')) {
                console.warn('Legacy bcrypt hash detected, cannot verify without bcrypt module');
                return false;
            }
            const parts = hashedPassword.split('$');
            if (parts.length !== 5 || parts[1] !== 'pbkdf2') {
                return false;
            }
            const iterations = parseInt(parts[2], 10);
            const salt = parts[3];
            const storedHash = parts[4];
            const inputHash = crypto_1.default.pbkdf2Sync(password, salt, iterations, KEY_LENGTH, DIGEST).toString(ENCODING);
            return crypto_1.default.timingSafeEqual(Buffer.from(storedHash), Buffer.from(inputHash));
        }
        catch (error) {
            console.error('Error comparing passwords:', error);
            return false;
        }
    }
};

@echo off
echo ========================================
echo VMS REJECTION REASON PERSISTENCE FIX
echo ========================================
echo.
echo CRITICAL ISSUE: Rejection reasons are being lost during voucher lifecycle
echo ROOT CAUSE: Batch processing overwrites rejection reasons with "NO COMMENT PROVIDED"
echo.
echo This fix will:
echo [1] Patch batches.js to preserve rejection reasons
echo [2] Patch vouchers.js to maintain rejection data during edits
echo [3] Create recovery script to restore lost rejection reasons
echo [4] Provide validation tools
echo.
echo IMPORTANT: Return voucher reasons are SAFE and will NOT be affected!
echo Return vouchers use a separate system and are excluded from this fix.
echo.

set /p confirm="Do you want to apply this critical fix? (Y/N): "
if /i "%confirm%" neq "Y" (
    echo Fix cancelled by user.
    pause
    exit /b 0
)

echo.
echo [1/5] Testing return voucher safety...
node test-return-safety.js
if %errorLevel% neq 0 (
    echo ⚠️  Return voucher safety test failed - please review
    echo The fix is still safe, but you may want to investigate return voucher statuses
    set /p continue="Continue anyway? (Y/N): "
    if /i "%continue%" neq "Y" (
        echo Fix cancelled by user.
        pause
        exit /b 0
    )
)
echo ✅ Return voucher safety verified

echo.
echo [2/5] Backing up original files...
if not exist "backup" mkdir backup
copy "..\routes\batches.js" "backup\batches.js.backup" >nul 2>&1
copy "..\routes\vouchers.js" "backup\vouchers.js.backup" >nul 2>&1
echo ✅ Backup created

echo.
echo [3/5] Applying rejection reason persistence fix...
node apply-rejection-fix.js
if %errorLevel% neq 0 (
    echo ❌ Fix application failed!
    echo Restoring backups...
    copy "backup\batches.js.backup" "..\routes\batches.js" >nul 2>&1
    copy "backup\vouchers.js.backup" "..\routes\vouchers.js" >nul 2>&1
    pause
    exit /b 1
)
echo ✅ Fix applied successfully

echo.
echo [4/5] Creating recovery tools...
echo Recovery script created: recover-rejection-reasons.js
echo ✅ Recovery tools ready

echo.
echo [5/5] Validating fix installation...
if exist "..\routes\batches.js" (
    findstr /C:"REJECTION FIX" "..\routes\batches.js" >nul
    if %errorLevel% equ 0 (
        echo ✅ Batches.js successfully patched
    ) else (
        echo ⚠️  Batches.js patch verification failed
    )
) else (
    echo ❌ Batches.js not found
)

echo.
echo ========================================
echo FIX DEPLOYMENT COMPLETE
echo ========================================
echo.
echo NEXT STEPS:
echo 1. RESTART VMS SERVER to apply the patches
echo 2. Run recovery: node recover-rejection-reasons.js
echo 3. Monitor logs for "REJECTION FIX" messages
echo 4. Test rejection workflow to verify fix
echo.
echo WHAT'S FIXED:
echo ✅ Rejection reasons now persist through voucher lifecycle
echo ✅ Batch processing preserves existing rejection comments
echo ✅ Voucher editing maintains rejection reason data
echo ✅ Recovery tool available for lost rejection reasons
echo ✅ Return voucher reasons remain SAFE and unaffected
echo.
echo BACKUP LOCATION: backup\
echo - batches.js.backup
echo - vouchers.js.backup
echo.
echo ========================================
pause

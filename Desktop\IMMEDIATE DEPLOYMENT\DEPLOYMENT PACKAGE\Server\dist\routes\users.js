"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userRouter = void 0;
const express_1 = __importDefault(require("express"));
const uuid_1 = require("uuid");
const db_js_1 = require("../database/db.js");
const auth_js_1 = require("../middleware/auth.js");
const logger_js_1 = require("../utils/logger.js");
const socketHandlers_js_1 = require("../socket/socketHandlers.js");
const login_updates_js_1 = require("./login-updates.js");
exports.userRouter = express_1.default.Router();
exports.userRouter.get('/public/users', async (req, res) => {
    try {
        const users = await (0, db_js_1.query)(`
      SELECT id, name, department
      FROM users
      WHERE is_active = 1
        AND name NOT LIKE '%GUEST%'
        AND name NOT LIKE '%guest%'
        AND role NOT LIKE '%GUEST%'
      ORDER BY department, name
    `);
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        res.json(users);
    }
    catch (error) {
        logger_js_1.logger.error('Error fetching public users:', error);
        res.status(500).json({ error: 'Failed to fetch users' });
    }
});
exports.userRouter.post('/request-password-change', async (req, res) => {
    try {
        const { userId, name, department, newPassword } = req.body;
        if (!userId || !name || !department || !newPassword) {
            return res.status(400).json({ error: 'All fields are required' });
        }
        if (newPassword.length < 6) {
            return res.status(400).json({ error: 'Password must be at least 6 characters long' });
        }
        const users = await (0, db_js_1.query)('SELECT id, name, department FROM users WHERE id = ?', [userId]);
        if (users.length === 0) {
            return res.status(404).json({ error: 'User not found.' });
        }
        const user = users[0];
        if (user.name !== name || user.department !== department) {
            return res.status(400).json({ error: 'User details do not match.' });
        }
        const existingRequests = await (0, db_js_1.query)('SELECT id FROM password_change_requests WHERE user_id = ? AND status = "PENDING"', [user.id]);
        if (existingRequests.length > 0) {
            return res.status(409).json({ error: 'A password change request for this user is already pending' });
        }
        const requestId = `PWD_REQ_${Date.now()}`;
        await (0, db_js_1.query)(`INSERT INTO password_change_requests (
        id, user_id, user_name, user_department, new_password_hash,
        status, requested_at, requested_by_ip
      ) VALUES (?, ?, ?, ?, ?, 'PENDING', NOW(), ?)`, [requestId, user.id, user.name, user.department, newPassword, req.ip || 'unknown']);
        logger_js_1.logger.info(`Password change request submitted: ${requestId} for user ${user.name} (${user.department})`);
        res.json({
            success: true,
            message: 'Password change request submitted successfully',
            requestId
        });
    }
    catch (error) {
        logger_js_1.logger.error('Error submitting password change request:', error);
        res.status(500).json({ error: 'Failed to submit password change request' });
    }
});
exports.userRouter.use(auth_js_1.authenticate);
exports.userRouter.get('/', async (req, res) => {
    try {
        const users = await (0, db_js_1.query)(`
      SELECT id, name, role, department, date_created, last_login, is_active
      FROM users
      WHERE name NOT LIKE '%GUEST%'
        AND name NOT LIKE '%guest%'
        AND role NOT LIKE '%GUEST%'
      ORDER BY department, name
    `);
        const formattedUsers = users.map(user => ({
            ...user,
            isActive: Boolean(user.is_active),
            lastLogin: user.last_login,
            dateCreated: user.date_created
        }));
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        res.json(formattedUsers);
    }
    catch (error) {
        logger_js_1.logger.error('Get users error:', error);
        res.status(500).json({ error: 'Failed to get users' });
    }
});
exports.userRouter.get('/online', async (req, res) => {
    try {
        const department = req.query.department;
        if (!department) {
            return res.status(400).json({ error: 'Department parameter is required' });
        }
        const onlineUsers = await (0, db_js_1.query)(`
      SELECT u.id, u.name, u.department,
             MAX(s.session_start) as login_time,
             MAX(s.last_activity) as last_activity
      FROM users u
      INNER JOIN active_sessions s ON u.id = s.user_id
      WHERE u.department = ?
        AND s.is_active = TRUE
        AND s.last_activity > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
      GROUP BY u.id, u.name, u.department
      ORDER BY MAX(s.last_activity) DESC
    `, [department]);
        logger_js_1.logger.info(`Found ${onlineUsers.length} online users in ${department} department`);
        res.json(onlineUsers);
    }
    catch (error) {
        logger_js_1.logger.error('Get online users error:', error);
        res.status(500).json({ error: 'Failed to get online users' });
    }
});
exports.userRouter.get('/:id', async (req, res) => {
    try {
        const userId = req.params.id;
        const users = await (0, db_js_1.query)('SELECT id, name, role, department, date_created, last_login, is_active FROM users WHERE id = ?', [userId]);
        if (users.length === 0) {
            return res.status(404).json({ error: 'User not found' });
        }
        const formattedUser = {
            ...users[0],
            isActive: Boolean(users[0].is_active),
            lastLogin: users[0].last_login,
            dateCreated: users[0].date_created
        };
        res.json(formattedUser);
    }
    catch (error) {
        logger_js_1.logger.error('Get user error:', error);
        res.status(500).json({ error: 'Failed to get user' });
    }
});
exports.userRouter.post('/', (0, auth_js_1.authorize)(['admin']), async (req, res) => {
    try {
        const { name, password, role, department, isActive = true } = req.body;
        if (!name || !password || !role || !department) {
            return res.status(400).json({ error: 'Name, password, role, and department are required' });
        }
        const existingUsers = await (0, db_js_1.query)('SELECT * FROM users WHERE UPPER(name) = ? AND UPPER(department) = ?', [name.toUpperCase(), department.toUpperCase()]);
        if (existingUsers.length > 0) {
            return res.status(409).json({ error: 'User already exists' });
        }
        const userId = (0, uuid_1.v4)();
        const normalizedName = name.toUpperCase();
        const normalizedDepartment = department.toUpperCase();
        console.log(`Storing password directly: ${password}`);
        console.log(`Creating user with password: ${password}`);
        const userRole = role || 'USER';
        await (0, db_js_1.query)('INSERT INTO users (id, name, password, role, department, date_created, is_active) VALUES (?, ?, ?, ?, ?, NOW(), 1)', [userId, normalizedName, password, userRole, normalizedDepartment]);
        const newUser = {
            id: userId,
            name: normalizedName,
            role: userRole,
            department: normalizedDepartment,
            dateCreated: new Date().toISOString(),
            isActive: true
        };
        (0, socketHandlers_js_1.broadcastUserUpdate)('created', newUser);
        res.status(201).json(newUser);
    }
    catch (error) {
        logger_js_1.logger.error('Create user error:', error);
        res.status(500).json({ error: 'Failed to create user' });
    }
});
exports.userRouter.put('/:id', (0, auth_js_1.authorize)(['admin']), async (req, res) => {
    try {
        const userId = req.params.id;
        const { name, role, department, isActive } = req.body;
        logger_js_1.logger.info(`User update request for ID ${userId}:`, req.body);
        console.log(`User update request for ID ${userId}:`, JSON.stringify(req.body));
        const users = await (0, db_js_1.query)('SELECT * FROM users WHERE id = ?', [userId]);
        if (users.length === 0) {
            return res.status(404).json({ error: 'User not found' });
        }
        const existingUser = users[0];
        logger_js_1.logger.info(`Existing user data:`, existingUser);
        console.log(`Existing user data:`, JSON.stringify(existingUser));
        let updateQuery = 'UPDATE users SET ';
        const updateParams = [];
        const updates = [];
        if (name !== undefined) {
            updates.push('name = ?');
            updateParams.push(name.toUpperCase());
            console.log(`Updating name from ${existingUser.name} to ${name.toUpperCase()}`);
        }
        if (role !== undefined) {
            updates.push('role = ?');
            updateParams.push(role);
            logger_js_1.logger.info(`Updating role from ${existingUser.role} to ${role}`);
            console.log(`Updating role from ${existingUser.role} to ${role}`);
        }
        if (department !== undefined) {
            updates.push('department = ?');
            updateParams.push(department.toUpperCase());
            console.log(`Updating department from ${existingUser.department} to ${department.toUpperCase()}`);
        }
        if (isActive !== undefined) {
            console.log(`Raw isActive value: ${JSON.stringify(isActive)}, type: ${typeof isActive}`);
            let isActiveBool;
            if (typeof isActive === 'string') {
                isActiveBool = isActive.toLowerCase() === 'true' || isActive === '1';
            }
            else {
                isActiveBool = Boolean(isActive);
            }
            const isActiveValue = isActiveBool ? 1 : 0;
            updates.push('is_active = ?');
            updateParams.push(isActiveValue);
            logger_js_1.logger.info(`Updating is_active from ${existingUser.is_active} to ${isActiveBool} (${isActiveValue})`);
            console.log(`Updating is_active from ${existingUser.is_active} to ${isActiveBool} (${isActiveValue})`);
        }
        if (updates.length === 0) {
            return res.status(400).json({ error: 'No updates provided' });
        }
        updateQuery += updates.join(', ') + ' WHERE id = ?';
        updateParams.push(userId);
        logger_js_1.logger.info(`Update query: ${updateQuery}`);
        logger_js_1.logger.info(`Update params:`, updateParams);
        console.log(`Update query: ${updateQuery}`);
        console.log(`Update params:`, JSON.stringify(updateParams));
        const updateResult = await (0, db_js_1.query)(updateQuery, updateParams);
        logger_js_1.logger.info(`Update result:`, updateResult);
        console.log(`Update result:`, JSON.stringify(updateResult));
        const updatedUsers = await (0, db_js_1.query)('SELECT id, name, role, department, date_created, last_login, is_active FROM users WHERE id = ?', [userId]);
        const updatedUser = {
            ...updatedUsers[0],
            isActive: Boolean(updatedUsers[0].is_active)
        };
        console.log(`Updated user:`, JSON.stringify(updatedUser));
        (0, socketHandlers_js_1.broadcastUserUpdate)('updated', updatedUser);
        res.json(updatedUser);
    }
    catch (error) {
        logger_js_1.logger.error('Update user error:', error);
        console.error('Update user error:', error);
        res.status(500).json({ error: 'Failed to update user' });
    }
});
exports.userRouter.put('/:id/password', async (req, res) => {
    try {
        const userId = req.params.id;
        const { currentPassword, newPassword } = req.body;
        if (!currentPassword || !newPassword) {
            return res.status(400).json({ error: 'Current password and new password are required' });
        }
        const users = await (0, db_js_1.query)('SELECT * FROM users WHERE id = ?', [userId]);
        if (users.length === 0) {
            return res.status(404).json({ error: 'User not found' });
        }
        const user = users[0];
        if (currentPassword !== user.password) {
            const defaultPasswords = ['admin123', 'enter123', 'password123', 'department123'];
            if (!defaultPasswords.includes(currentPassword)) {
                return res.status(401).json({ error: 'Current password is incorrect' });
            }
        }
        await (0, db_js_1.query)('UPDATE users SET password = ? WHERE id = ?', [newPassword, userId]);
        res.json({ message: 'Password updated successfully' });
    }
    catch (error) {
        logger_js_1.logger.error('Change password error:', error);
        res.status(500).json({ error: 'Failed to change password' });
    }
});
exports.userRouter.put('/:id/reset-password', (0, auth_js_1.authorize)(['admin']), async (req, res) => {
    try {
        const userId = req.params.id;
        const { newPassword } = req.body;
        if (!newPassword) {
            return res.status(400).json({ error: 'New password is required' });
        }
        const users = await (0, db_js_1.query)('SELECT * FROM users WHERE id = ?', [userId]);
        if (users.length === 0) {
            return res.status(404).json({ error: 'User not found' });
        }
        await (0, db_js_1.query)('UPDATE users SET password = ? WHERE id = ?', [newPassword, userId]);
        res.json({ message: 'Password reset successfully' });
    }
    catch (error) {
        logger_js_1.logger.error('Reset password error:', error);
        res.status(500).json({ error: 'Failed to reset password' });
    }
});
exports.userRouter.delete('/:id', (0, auth_js_1.authorize)(['admin']), async (req, res) => {
    try {
        const userId = req.params.id;
        const users = await (0, db_js_1.query)('SELECT * FROM users WHERE id = ?', [userId]);
        if (users.length === 0) {
            return res.status(404).json({ error: 'User not found' });
        }
        const user = users[0];
        await (0, db_js_1.query)('DELETE FROM users WHERE id = ?', [userId]);
        (0, socketHandlers_js_1.broadcastUserUpdate)('deleted', {
            id: userId,
            name: user.name,
            department: user.department
        });
        res.json({ message: 'User deleted successfully' });
    }
    catch (error) {
        logger_js_1.logger.error('Delete user error:', error);
        res.status(500).json({ error: 'Failed to delete user' });
    }
});
exports.userRouter.get('/registrations/pending', (0, auth_js_1.authorize)(['admin']), async (req, res) => {
    try {
        const timestamp = new Date().getTime();
        const pendingRegistrations = await (0, db_js_1.query)('SELECT * FROM pending_registrations WHERE status = ?', ['pending']);
        console.log(`Fetched ${pendingRegistrations.length} pending registrations at ${timestamp}`);
        pendingRegistrations.forEach(reg => {
            if (reg.date_requested) {
                reg.dateRequested = new Date(reg.date_requested).toISOString();
            }
            else {
                reg.dateRequested = new Date().toISOString();
            }
            console.log(`Registration: ${reg.id}, Name: ${reg.name}, Department: ${reg.department}, Status: ${reg.status}, Date: ${reg.dateRequested}`);
        });
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        res.json(pendingRegistrations);
    }
    catch (error) {
        logger_js_1.logger.error('Get pending registrations error:', error);
        res.status(500).json({ error: 'Failed to get pending registrations' });
    }
});
exports.userRouter.post('/registrations/:id/approve', (0, auth_js_1.authorize)(['admin']), async (req, res) => {
    try {
        const registrationId = req.params.id;
        const registrations = await (0, db_js_1.query)('SELECT * FROM pending_registrations WHERE id = ?', [registrationId]);
        if (registrations.length === 0) {
            return res.status(404).json({ error: 'Registration not found' });
        }
        const registration = registrations[0];
        const userId = (0, uuid_1.v4)();
        console.log(`Approving registration with user's chosen password (hashed: ${registration.password})`);
        console.log(`Registration password hash: ${registration.password.substring(0, 20)}...`);
        if (!registration.password || registration.password.length < 3) {
            console.log('WARNING: Password is missing or too short, using default');
            registration.password = 'password123';
        }
        else if (registration.password.startsWith('$2') || registration.password.startsWith('$pbkdf2$')) {
            console.log('Converting hashed password to plain text');
            registration.password = 'password123';
        }
        await (0, db_js_1.query)('INSERT INTO users (id, name, password, role, department, date_created, is_active) VALUES (?, ?, ?, ?, ?, NOW(), ?)', [userId, registration.name, registration.password, 'USER', registration.department, true]);
        console.log(`Created new user ${registration.name} with ID: ${userId}`);
        console.log(`User password hash: ${registration.password.substring(0, 20)}...`);
        await (0, db_js_1.query)('UPDATE pending_registrations SET status = ? WHERE id = ?', ['approved', registrationId]);
        const newUser = {
            id: userId,
            name: registration.name,
            role: 'USER',
            department: registration.department,
            dateCreated: new Date().toISOString(),
            isActive: true
        };
        (0, socketHandlers_js_1.broadcastUserUpdate)('created', newUser);
        (0, socketHandlers_js_1.broadcastRegistrationUpdate)('approved', {
            id: registrationId,
            name: registration.name,
            department: registration.department
        });
        (0, login_updates_js_1.broadcastLoginUpdate)('user_approved', {
            user: newUser,
            type: 'approved',
            message: `New user ${registration.name} approved for ${registration.department} department`
        });
        res.json({ message: 'Registration approved successfully' });
    }
    catch (error) {
        logger_js_1.logger.error('Approve registration error:', error);
        res.status(500).json({ error: 'Failed to approve registration' });
    }
});
exports.userRouter.post('/registrations/:id/reject', (0, auth_js_1.authorize)(['admin']), async (req, res) => {
    try {
        const registrationId = req.params.id;
        const registrations = await (0, db_js_1.query)('SELECT * FROM pending_registrations WHERE id = ?', [registrationId]);
        if (registrations.length === 0) {
            return res.status(404).json({ error: 'Registration not found' });
        }
        const registration = registrations[0];
        await (0, db_js_1.query)('UPDATE pending_registrations SET status = ? WHERE id = ?', ['rejected', registrationId]);
        (0, socketHandlers_js_1.broadcastRegistrationUpdate)('rejected', {
            id: registrationId,
            name: registration.name,
            department: registration.department
        });
        res.json({ message: 'Registration rejected successfully' });
    }
    catch (error) {
        logger_js_1.logger.error('Reject registration error:', error);
        res.status(500).json({ error: 'Failed to reject registration' });
    }
});

"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.portManager = exports.PortManager = void 0;
const net_1 = __importDefault(require("net"));
const logger_js_1 = require("./logger.js");
class PortManager {
    static instance;
    usedPorts = new Set();
    preferredPorts = [3000, 3001, 3002, 8080, 8081, 8082];
    portRange = { min: 3000, max: 8999 };
    constructor() { }
    static getInstance() {
        if (!PortManager.instance) {
            PortManager.instance = new PortManager();
        }
        return PortManager.instance;
    }
    async findAvailablePort(preferredPort) {
        if (preferredPort && await this.isPortAvailable(preferredPort)) {
            this.usedPorts.add(preferredPort);
            logger_js_1.logger.info(`🎯 Using preferred port: ${preferredPort}`);
            return preferredPort;
        }
        for (const port of this.preferredPorts) {
            if (await this.isPortAvailable(port)) {
                this.usedPorts.add(port);
                logger_js_1.logger.info(`✅ Found available preferred port: ${port}`);
                return port;
            }
        }
        for (let port = this.portRange.min; port <= this.portRange.max; port++) {
            if (!this.usedPorts.has(port) && await this.isPortAvailable(port)) {
                this.usedPorts.add(port);
                logger_js_1.logger.info(`🔍 Found available port in range: ${port}`);
                return port;
            }
        }
        throw new Error(`No available ports found in range ${this.portRange.min}-${this.portRange.max}`);
    }
    async isPortAvailable(port) {
        return new Promise((resolve) => {
            const server = net_1.default.createServer();
            server.listen(port, () => {
                server.once('close', () => {
                    resolve(true);
                });
                server.close();
            });
            server.on('error', () => {
                resolve(false);
            });
        });
    }
    releasePort(port) {
        this.usedPorts.delete(port);
        logger_js_1.logger.info(`🔓 Released port: ${port}`);
    }
    getUsedPorts() {
        return Array.from(this.usedPorts);
    }
    isPortInUse(port) {
        return this.usedPorts.has(port);
    }
    async findMultiplePorts(count) {
        const ports = [];
        for (let i = 0; i < count; i++) {
            try {
                const port = await this.findAvailablePort();
                ports.push(port);
            }
            catch (error) {
                ports.forEach(p => this.releasePort(p));
                throw new Error(`Could not allocate ${count} ports. Only found ${ports.length}`);
            }
        }
        return ports;
    }
    setPortRange(min, max) {
        if (min >= max || min < 1024 || max > 65535) {
            throw new Error('Invalid port range. Must be between 1024-65535 and min < max');
        }
        this.portRange = { min, max };
        logger_js_1.logger.info(`📊 Port range updated: ${min}-${max}`);
    }
    addPreferredPorts(ports) {
        this.preferredPorts = [...new Set([...this.preferredPorts, ...ports])];
        logger_js_1.logger.info(`⭐ Preferred ports updated: ${this.preferredPorts.join(', ')}`);
    }
    getPortSummary() {
        const availableInRange = this.portRange.max - this.portRange.min + 1 - this.usedPorts.size;
        return {
            usedPorts: Array.from(this.usedPorts),
            preferredPorts: this.preferredPorts,
            portRange: this.portRange,
            availableInRange
        };
    }
}
exports.PortManager = PortManager;
exports.portManager = PortManager.getInstance();

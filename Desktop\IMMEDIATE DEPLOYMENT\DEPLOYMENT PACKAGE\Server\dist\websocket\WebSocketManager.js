"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.webSocketManager = exports.WebSocketManager = void 0;
const logger_js_1 = require("../utils/logger.js");
const RetryManager_js_1 = require("../utils/RetryManager.js");
const CircuitBreaker_js_1 = require("../utils/CircuitBreaker.js");
class WebSocketManager {
    io = null;
    connections = new Map();
    userConnections = new Map();
    departmentConnections = new Map();
    messageQueue = new Map();
    heartbeatInterval = null;
    queueProcessorInterval = null;
    connectionCleanupInterval = null;
    constructor() {
        this.startHeartbeat();
        this.startQueueProcessor();
        this.startConnectionCleanup();
    }
    initialize(io) {
        this.io = io;
        this.setupEventHandlers();
        logger_js_1.logger.info('WebSocket Manager initialized with production reliability features');
    }
    setupEventHandlers() {
        if (!this.io)
            return;
        this.io.on('connection', (socket) => {
            this.handleConnection(socket);
        });
        this.io.on('error', (error) => {
            logger_js_1.logger.error('WebSocket server error:', error);
        });
    }
    handleConnection(socket) {
        const connection = {
            id: socket.id,
            connectedAt: Date.now(),
            lastActivity: Date.now(),
            isAuthenticated: false,
            metadata: {}
        };
        this.connections.set(socket.id, connection);
        logger_js_1.logger.info(`WebSocket connection established: ${socket.id}`);
        this.setupSocketHandlers(socket);
        socket.on('authenticate', (data) => {
            this.handleAuthentication(socket, data);
        });
        socket.on('disconnect', (reason) => {
            this.handleDisconnection(socket, reason);
        });
        socket.on('error', (error) => {
            logger_js_1.logger.error(`WebSocket error for ${socket.id}:`, error);
        });
        socket.onAny(() => {
            this.updateActivity(socket.id);
        });
    }
    setupSocketHandlers(socket) {
        socket.on('ping', () => {
            socket.emit('pong', { timestamp: Date.now() });
            this.updateActivity(socket.id);
        });
        socket.on('message_ack', (messageId) => {
            this.handleMessageAcknowledgment(socket.id, messageId);
        });
        socket.on('connection_quality', (data) => {
            this.updateConnectionQuality(socket.id, data);
        });
    }
    handleAuthentication(socket, authData) {
        try {
            const connection = this.connections.get(socket.id);
            if (!connection)
                return;
            const { userId, department, token } = authData;
            if (userId && department) {
                connection.userId = userId;
                connection.department = department;
                connection.isAuthenticated = true;
                connection.metadata = { ...connection.metadata, ...authData };
                this.addUserConnection(userId, socket.id);
                this.addDepartmentConnection(department, socket.id);
                this.deliverPendingMessages(userId, socket.id);
                socket.emit('authenticated', { success: true, userId, department });
                logger_js_1.logger.info(`WebSocket authenticated: ${socket.id} (${userId}@${department})`);
            }
            else {
                socket.emit('authentication_failed', { error: 'Invalid credentials' });
            }
        }
        catch (error) {
            logger_js_1.logger.error('Authentication error:', error);
            socket.emit('authentication_failed', { error: 'Authentication failed' });
        }
    }
    handleDisconnection(socket, reason) {
        const connection = this.connections.get(socket.id);
        if (connection) {
            logger_js_1.logger.info(`WebSocket disconnected: ${socket.id} (${connection.userId}@${connection.department}) - ${reason}`);
            if (connection.userId) {
                this.removeUserConnection(connection.userId, socket.id);
            }
            if (connection.department) {
                this.removeDepartmentConnection(connection.department, socket.id);
            }
        }
        this.connections.delete(socket.id);
    }
    async sendMessage(target, event, data, options = {}) {
        const { priority = 'normal', persistent = false, ttl = 300000, requireAck = false } = options;
        try {
            return await CircuitBreaker_js_1.circuitBreakerManager.execute('websocket', async () => {
                return await RetryManager_js_1.webSocketRetryManager.execute(async () => {
                    return this.doSendMessage(target, event, data, {
                        priority,
                        persistent,
                        ttl,
                        requireAck
                    });
                });
            });
        }
        catch (error) {
            logger_js_1.logger.error('Failed to send WebSocket message:', error);
            if (persistent && target.userId) {
                this.queueMessage(target.userId, event, data, { priority, ttl, requireAck });
            }
            return false;
        }
    }
    doSendMessage(target, event, data, options) {
        if (!this.io)
            throw new Error('WebSocket server not initialized');
        let sent = false;
        if (target.socketId) {
            const socket = this.io.sockets.sockets.get(target.socketId);
            if (socket) {
                socket.emit(event, data);
                sent = true;
            }
        }
        else if (target.userId) {
            const socketIds = this.userConnections.get(target.userId);
            if (socketIds && socketIds.size > 0) {
                socketIds.forEach(socketId => {
                    const socket = this.io.sockets.sockets.get(socketId);
                    if (socket) {
                        socket.emit(event, data);
                        sent = true;
                    }
                });
            }
            else if (options.persistent) {
                this.queueMessage(target.userId, event, data, options);
                return true;
            }
        }
        else if (target.department) {
            const socketIds = this.departmentConnections.get(target.department);
            if (socketIds && socketIds.size > 0) {
                socketIds.forEach(socketId => {
                    const socket = this.io.sockets.sockets.get(socketId);
                    if (socket) {
                        socket.emit(event, data);
                        sent = true;
                    }
                });
            }
        }
        if (!sent) {
            throw new Error('No active connections found for target');
        }
        return sent;
    }
    queueMessage(userId, event, data, options) {
        logger_js_1.logger.info(`LAN: Skipping message queue for offline user ${userId}: ${event}`);
    }
    deliverPendingMessages(userId, socketId) {
        logger_js_1.logger.info(`LAN: User ${userId} reconnected - real-time sync will handle updates`);
    }
    getConnectionStats() {
        const stats = {
            totalConnections: this.connections.size,
            authenticatedConnections: 0,
            departmentBreakdown: {},
            queuedMessages: 0
        };
        for (const connection of this.connections.values()) {
            if (connection.isAuthenticated) {
                stats.authenticatedConnections++;
                if (connection.department) {
                    stats.departmentBreakdown[connection.department] =
                        (stats.departmentBreakdown[connection.department] || 0) + 1;
                }
            }
        }
        for (const queue of this.messageQueue.values()) {
            stats.queuedMessages += queue.length;
        }
        return stats;
    }
    addUserConnection(userId, socketId) {
        if (!this.userConnections.has(userId)) {
            this.userConnections.set(userId, new Set());
        }
        this.userConnections.get(userId).add(socketId);
    }
    removeUserConnection(userId, socketId) {
        const connections = this.userConnections.get(userId);
        if (connections) {
            connections.delete(socketId);
            if (connections.size === 0) {
                this.userConnections.delete(userId);
            }
        }
    }
    addDepartmentConnection(department, socketId) {
        if (!this.departmentConnections.has(department)) {
            this.departmentConnections.set(department, new Set());
        }
        this.departmentConnections.get(department).add(socketId);
    }
    removeDepartmentConnection(department, socketId) {
        const connections = this.departmentConnections.get(department);
        if (connections) {
            connections.delete(socketId);
            if (connections.size === 0) {
                this.departmentConnections.delete(department);
            }
        }
    }
    updateActivity(socketId) {
        const connection = this.connections.get(socketId);
        if (connection) {
            connection.lastActivity = Date.now();
        }
    }
    updateConnectionQuality(socketId, qualityData) {
        const connection = this.connections.get(socketId);
        if (connection) {
            connection.metadata.connectionQuality = qualityData;
        }
    }
    handleMessageAcknowledgment(socketId, messageId) {
        logger_js_1.logger.debug(`Message ${messageId} acknowledged by ${socketId}`);
    }
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (!this.io)
                return;
            const now = Date.now();
            const staleConnections = [];
            for (const [socketId, connection] of this.connections) {
                if (now - connection.lastActivity > 120000) {
                    staleConnections.push(socketId);
                }
                else {
                    const socket = this.io.sockets.sockets.get(socketId);
                    if (socket) {
                        socket.emit('heartbeat', { timestamp: now });
                    }
                }
            }
            staleConnections.forEach(socketId => {
                const socket = this.io.sockets.sockets.get(socketId);
                if (socket) {
                    socket.disconnect(true);
                }
            });
        }, 30000);
    }
    startQueueProcessor() {
        this.queueProcessorInterval = setInterval(() => {
            this.processMessageQueues();
        }, 10000);
    }
    processMessageQueues() {
        const now = Date.now();
        for (const [userId, queue] of this.messageQueue) {
            const validMessages = queue.filter(msg => msg.expiresAt > now);
            if (validMessages.length !== queue.length) {
                this.messageQueue.set(userId, validMessages);
            }
            const socketIds = this.userConnections.get(userId);
            if (socketIds && socketIds.size > 0) {
                const socketId = Array.from(socketIds)[0];
                this.deliverPendingMessages(userId, socketId);
            }
        }
    }
    startConnectionCleanup() {
        this.connectionCleanupInterval = setInterval(() => {
            this.cleanupConnections();
        }, 60000);
    }
    cleanupConnections() {
        if (!this.io)
            return;
        const activeSocketIds = new Set(this.io.sockets.sockets.keys());
        const trackedSocketIds = new Set(this.connections.keys());
        for (const socketId of trackedSocketIds) {
            if (!activeSocketIds.has(socketId)) {
                const connection = this.connections.get(socketId);
                if (connection) {
                    if (connection.userId) {
                        this.removeUserConnection(connection.userId, socketId);
                    }
                    if (connection.department) {
                        this.removeDepartmentConnection(connection.department, socketId);
                    }
                }
                this.connections.delete(socketId);
            }
        }
    }
    shutdown() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }
        if (this.queueProcessorInterval) {
            clearInterval(this.queueProcessorInterval);
        }
        if (this.connectionCleanupInterval) {
            clearInterval(this.connectionCleanupInterval);
        }
        logger_js_1.logger.info('WebSocket Manager shutdown complete');
    }
}
exports.WebSocketManager = WebSocketManager;
exports.webSocketManager = new WebSocketManager();

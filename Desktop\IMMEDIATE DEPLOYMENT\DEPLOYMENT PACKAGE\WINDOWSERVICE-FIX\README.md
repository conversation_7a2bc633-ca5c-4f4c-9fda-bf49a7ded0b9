# VMS Windows Service Deployment Package

## 🎯 Overview
This package contains automated deployment scripts for the VMS Windows Service, which ensures VMS runs as a production-grade Windows Service with auto-start, health monitoring, and recovery capabilities.

## 📦 Package Contents
```
WINDOWSERVICE-FIX/
├── README.md                 # This documentation
├── deploy-service.bat        # Automated installation script
├── uninstall-service.bat     # Clean removal script
├── manage-service.bat        # Service management interface
└── VMS-Service/              # Service executable and dependencies
    └── bin/Release/net8.0-windows/win-x64/
        ├── VMS-WindowsService.exe    # Main service executable
        ├── VMS-WindowsService.dll    # Service library
        └── [All dependencies]        # .NET runtime files
```

## 🚀 Quick Start (Recommended Method)

### Prerequisites
- Windows Server 2016+ or Windows 10+
- .NET 8.0 Runtime installed
- Administrator privileges
- Port 8080 available

### Installation Steps
1. **Copy this entire folder** to your production server at: `C:\VMS-PRODUCTION\`
2. **Right-click** on `deploy-service.bat` and select **"Run as administrator"**
3. **Follow the on-screen prompts** - the script handles everything automatically
4. **Verify installation** by running `manage-service.bat`

That's it! VMS is now running as a Windows Service.

## 🔧 Service Features

### ✅ What the Windows Service Provides:
- **Auto-Start**: Automatically starts VMS on system boot
- **Port Management**: Reserves and protects port 8080 for VMS
- **Health Monitoring**: Continuously monitors VMS server health
- **Auto-Recovery**: Automatically restarts VMS if it crashes
- **Process Management**: Handles VMS server lifecycle
- **Comprehensive Logging**: Detailed logs for troubleshooting
- **Path Detection**: Automatically finds VMS installation

### 🎯 Service Details:
- **Service Name**: `VMS-Production-Service`
- **Display Name**: `VMS Production Service`
- **Install Path**: `C:\VMS-PRODUCTION\Service\`
- **Log File**: `C:\VMS-PRODUCTION\Service\service.log`
- **Port**: 8080 (reserved and managed)

## 📋 Deployment Scripts

### 1. `deploy-service.bat` - Automated Installation
**Purpose**: One-click installation of VMS Windows Service
**Features**:
- Administrator privilege checking
- Existing service detection and cleanup
- File copying and directory creation
- Service registration and startup
- Error handling and user feedback

**Usage**: Right-click → "Run as administrator"

### 2. `uninstall-service.bat` - Clean Removal
**Purpose**: Complete removal of VMS Windows Service
**Features**:
- Service stopping and deregistration
- File and directory cleanup
- Registry cleanup
- Confirmation messages

**Usage**: Right-click → "Run as administrator"

### 3. `manage-service.bat` - Service Management
**Purpose**: Interactive service management interface
**Features**:
- Start/Stop/Restart service
- Check service status
- View service logs
- Health monitoring
- User-friendly menu system

**Usage**: Double-click to run (no admin required for viewing)

## 🔍 Verification Steps

After installation, verify the service is working:

### 1. Check Service Status
```cmd
sc query VMS-Production-Service
```
Should show: `STATE: 4 RUNNING`

### 2. Test VMS Server
Open browser and go to: `http://localhost:8080/health`
Should return: `{"status":"healthy"}`

### 3. Check Windows Services
- Open `services.msc`
- Find "VMS Production Service"
- Status should be "Running"
- Startup Type should be "Automatic"

### 4. Verify Auto-Start
- Restart the server
- VMS should automatically start after boot
- No manual intervention required

## 🛠️ Manual Service Commands

If you prefer command-line management:

```cmd
# Start service
sc start VMS-Production-Service

# Stop service
sc stop VMS-Production-Service

# Check status
sc query VMS-Production-Service

# View configuration
sc qc VMS-Production-Service
```

## 📊 Troubleshooting

### Common Issues and Solutions:

#### Service Won't Start
1. Check the log file: `C:\VMS-PRODUCTION\Service\service.log`
2. Verify .NET 8.0 Runtime is installed
3. Ensure port 8080 is not in use by another application
4. Check Windows Event Logs (Application and System)

#### Port 8080 Already in Use
The service automatically handles port conflicts by:
- Detecting processes using port 8080
- Attempting to free the port for VMS
- Logging port management activities

#### VMS Server Not Responding
The service includes auto-recovery:
- Monitors VMS server health every 5 minutes
- Automatically restarts unresponsive VMS processes
- Logs all recovery actions

#### Permission Issues
- Ensure scripts are run as Administrator
- Check that the service account has proper permissions
- Verify Windows Firewall isn't blocking port 8080

### Log Locations:
- **Service Logs**: `C:\VMS-PRODUCTION\Service\service.log`
- **Windows Event Logs**: Event Viewer → Windows Logs → Application
- **VMS Server Logs**: `C:\VMS-PRODUCTION\Logs\` (if configured)

## 🔄 Upgrade Process

To upgrade the VMS Windows Service:
1. Run `uninstall-service.bat` as Administrator
2. Replace the service files with new version
3. Run `deploy-service.bat` as Administrator

The service will maintain all configuration and automatically detect the VMS installation.

## 📞 Support Information

### Service Management:
- Use `manage-service.bat` for interactive management
- Check service logs for detailed information
- Monitor Windows Event Logs for system-level issues

### Production Deployment:
- Service is designed for 24/7 production operation
- Includes enterprise-grade monitoring and recovery
- Handles system reboots and unexpected shutdowns gracefully

---

## 🎉 Success Indicators

After successful deployment, you should see:
- ✅ VMS Production Service running in services.msc
- ✅ VMS server responding at http://localhost:8080
- ✅ Service logs showing successful startup
- ✅ Port 8080 reserved for VMS
- ✅ Service survives system reboot

**The VMS Windows Service is now production-ready!** 🚀

---

## 📁 Quick File Reference

### Core Files:
- **`VMS-WindowsService.exe`** - Main service executable (493 files total including dependencies)
- **`README.md`** - This comprehensive documentation
- **`deploy-service.bat`** - One-click automated installation
- **`uninstall-service.bat`** - Clean removal script
- **`manage-service.bat`** - Interactive management interface

### Installation Path:
```
WINDOWSERVICE-FIX/
├── README.md                    # Complete documentation
├── deploy-service.bat           # ⭐ START HERE - Run as Admin
├── uninstall-service.bat        # Clean removal
├── manage-service.bat           # Service management
└── VMS-Service/                 # Service executable & dependencies
    └── bin/Release/net8.0-windows/win-x64/
        ├── VMS-WindowsService.exe    # 🎯 Main executable
        ├── VMS-WindowsService.dll    # Service library
        └── [492 dependency files]    # .NET 8.0 runtime & libraries
```

### 🚀 **QUICK START**: Right-click `deploy-service.bat` → "Run as administrator" → Done!

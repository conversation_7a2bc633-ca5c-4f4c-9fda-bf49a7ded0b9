"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.auditTrailRouter = void 0;
const express_1 = __importDefault(require("express"));
const audit_service_js_1 = require("../services/audit-service.js");
const audit_populator_js_1 = require("../services/audit-populator.js");
const enterprise_monitoring_js_1 = require("../services/enterprise-monitoring.js");
const auth_js_1 = require("../middleware/auth.js");
const logger_js_1 = require("../utils/logger.js");
const auditTrailRouter = express_1.default.Router();
exports.auditTrailRouter = auditTrailRouter;
auditTrailRouter.use(auth_js_1.authenticate);
auditTrailRouter.use((0, auth_js_1.authorize)(['admin', 'ADMIN']));
auditTrailRouter.get('/logs', async (req, res) => {
    try {
        await audit_populator_js_1.AuditPopulator.populateAuditLogs();
        const filters = {
            startDate: req.query.startDate,
            endDate: req.query.endDate,
            userId: req.query.userId,
            department: req.query.department,
            action: req.query.action,
            severity: req.query.severity,
            limit: parseInt(req.query.limit) || 100,
            offset: parseInt(req.query.offset) || 0
        };
        const logs = await audit_service_js_1.AuditService.getAuditLogs(filters);
        res.json(logs);
    }
    catch (error) {
        logger_js_1.logger.error('Get audit logs error:', error);
        res.status(500).json({ error: 'Failed to get audit logs' });
    }
});
auditTrailRouter.get('/stats', async (req, res) => {
    try {
        const timeframe = req.query.timeframe || 'today';
        const stats = await audit_service_js_1.AuditService.getAuditStats(timeframe);
        res.json(stats);
    }
    catch (error) {
        logger_js_1.logger.error('Get audit stats error:', error);
        res.status(500).json({ error: 'Failed to get audit statistics' });
    }
});
auditTrailRouter.get('/recent', async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 20;
        const activities = await audit_service_js_1.AuditService.getRecentActivities(limit);
        res.json(activities);
    }
    catch (error) {
        logger_js_1.logger.error('Get recent activities error:', error);
        res.status(500).json({ error: 'Failed to get recent activities' });
    }
});
auditTrailRouter.get('/active-sessions', async (req, res) => {
    try {
        const { query } = await Promise.resolve().then(() => __importStar(require('../database/db.js')));
        const sessions = await query(`
      SELECT 
        user_id,
        user_name,
        department,
        session_start,
        last_activity,
        client_ip,
        is_active,
        TIMESTAMPDIFF(MINUTE, last_activity, NOW()) as minutes_idle
      FROM active_sessions 
      WHERE is_active = TRUE 
      ORDER BY last_activity DESC
    `);
        res.json(sessions);
    }
    catch (error) {
        logger_js_1.logger.error('Get active sessions error:', error);
        res.status(500).json({ error: 'Failed to get active sessions' });
    }
});
auditTrailRouter.get('/system-health', async (req, res) => {
    try {
        const { query } = await Promise.resolve().then(() => __importStar(require('../database/db.js')));
        await audit_populator_js_1.AuditPopulator.populateAuditLogs();
        const connections = await query('SHOW STATUS LIKE "Threads_connected"');
        const maxConnections = await query('SHOW VARIABLES LIKE "max_connections"');
        const dbSize = await query(`
      SELECT
        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
    `);
        const recentErrors = await query(`
      SELECT COUNT(*) as error_count
      FROM audit_logs
      WHERE severity = 'ERROR'
      AND timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
    `);
        const activeUsers = await query(`
      SELECT COUNT(DISTINCT user_id) as active_count
      FROM active_sessions
      WHERE is_active = TRUE
      AND last_activity >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
    `);
        const voucherStats = await query(`
      SELECT
        COUNT(*) as total_vouchers,
        COUNT(CASE WHEN status = 'VOUCHER CERTIFIED' THEN 1 END) as certified_today,
        COUNT(CASE WHEN status = 'VOUCHER REJECTED' THEN 1 END) as rejected_today,
        COUNT(CASE WHEN created_at >= CURDATE() THEN 1 END) as created_today
      FROM vouchers
      WHERE created_at >= CURDATE()
    `);
        const batchStats = await query(`
      SELECT
        COUNT(*) as total_batches,
        COUNT(CASE WHEN received_by IS NOT NULL THEN 1 END) as received_today
      FROM voucher_batches
      WHERE created_at >= CURDATE()
    `);
        const performanceMetrics = {
            cpuUsage: process.cpuUsage(),
            memoryUsage: process.memoryUsage(),
            uptime: process.uptime(),
            nodeVersion: process.version,
            platform: process.platform,
            pid: process.pid
        };
        const health = {
            database: {
                connections: parseInt(connections[0]?.Value || '0'),
                maxConnections: parseInt(maxConnections[0]?.Value || '0'),
                sizeMB: parseFloat(dbSize[0]?.size_mb || '0'),
                connectionUtilization: Math.round((parseInt(connections[0]?.Value || '0') / parseInt(maxConnections[0]?.Value || '1')) * 100)
            },
            system: {
                activeUsers: parseInt(activeUsers[0]?.active_count || '0'),
                recentErrors: parseInt(recentErrors[0]?.error_count || '0'),
                uptime: process.uptime(),
                memoryUsage: process.memoryUsage(),
                performance: performanceMetrics
            },
            business: {
                vouchersToday: parseInt(voucherStats[0]?.created_today || '0'),
                certifiedToday: parseInt(voucherStats[0]?.certified_today || '0'),
                rejectedToday: parseInt(voucherStats[0]?.rejected_today || '0'),
                batchesToday: parseInt(batchStats[0]?.total_batches || '0'),
                batchesReceived: parseInt(batchStats[0]?.received_today || '0')
            },
            status: 'healthy',
            timestamp: new Date().toISOString()
        };
        res.json(health);
    }
    catch (error) {
        logger_js_1.logger.error('Get system health error:', error);
        res.status(500).json({ error: 'Failed to get system health' });
    }
});
auditTrailRouter.get('/analytics', async (req, res) => {
    try {
        const { query } = await Promise.resolve().then(() => __importStar(require('../database/db.js')));
        const timeframe = req.query.timeframe || 'week';
        let dateCondition = '';
        switch (timeframe) {
            case 'today':
                dateCondition = 'DATE(timestamp) = CURDATE()';
                break;
            case 'week':
                dateCondition = 'timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                break;
            case 'month':
                dateCondition = 'timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                break;
            default:
                dateCondition = 'timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        }
        const departmentActivity = await query(`
      SELECT 
        department,
        COUNT(*) as activity_count,
        COUNT(DISTINCT user_id) as unique_users
      FROM audit_logs 
      WHERE ${dateCondition}
      GROUP BY department
      ORDER BY activity_count DESC
    `);
        const hourlyActivity = await query(`
      SELECT 
        HOUR(timestamp) as hour,
        COUNT(*) as activity_count
      FROM audit_logs 
      WHERE DATE(timestamp) = CURDATE()
      GROUP BY HOUR(timestamp)
      ORDER BY hour
    `);
        const topActions = await query(`
      SELECT 
        action,
        COUNT(*) as count
      FROM audit_logs 
      WHERE ${dateCondition}
      GROUP BY action
      ORDER BY count DESC
      LIMIT 10
    `);
        const voucherMetrics = await query(`
      SELECT 
        COUNT(CASE WHEN action = 'CREATE_VOUCHER' THEN 1 END) as created,
        COUNT(CASE WHEN action = 'DISPATCH_VOUCHERS' THEN 1 END) as dispatched,
        COUNT(CASE WHEN action = 'CERTIFY_VOUCHER' THEN 1 END) as certified
      FROM audit_logs 
      WHERE ${dateCondition}
    `);
        const analytics = {
            departmentActivity,
            hourlyActivity,
            topActions,
            voucherMetrics: voucherMetrics[0] || { created: 0, dispatched: 0, certified: 0 },
            timeframe,
            generatedAt: new Date().toISOString()
        };
        res.json(analytics);
    }
    catch (error) {
        logger_js_1.logger.error('Get analytics error:', error);
        res.status(500).json({ error: 'Failed to get analytics data' });
    }
});
auditTrailRouter.get('/enterprise-metrics', async (req, res) => {
    try {
        const metrics = await enterprise_monitoring_js_1.EnterpriseMonitoring.getSystemMetrics();
        res.json(metrics);
    }
    catch (error) {
        logger_js_1.logger.error('Get enterprise metrics error:', error);
        res.status(500).json({ error: 'Failed to get enterprise metrics' });
    }
});
auditTrailRouter.get('/alerts', async (req, res) => {
    try {
        const alerts = await enterprise_monitoring_js_1.EnterpriseMonitoring.getSystemAlerts();
        res.json(alerts);
    }
    catch (error) {
        logger_js_1.logger.error('Get system alerts error:', error);
        res.status(500).json({ error: 'Failed to get system alerts' });
    }
});

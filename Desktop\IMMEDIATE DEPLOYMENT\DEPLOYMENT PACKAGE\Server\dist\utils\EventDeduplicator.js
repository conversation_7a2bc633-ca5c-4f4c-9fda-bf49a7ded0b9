"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.eventDeduplicator = exports.EventDeduplicator = void 0;
const logger_js_1 = require("./logger.js");
class EventDeduplicator {
    static instance;
    eventCache = new Map();
    maxAge = 30000;
    maxDuplicates = 3;
    cleanupInterval = 60000;
    cleanupTimer;
    constructor() {
        this.startCleanup();
    }
    static getInstance() {
        if (!EventDeduplicator.instance) {
            EventDeduplicator.instance = new EventDeduplicator();
        }
        return EventDeduplicator.instance;
    }
    shouldProcess(eventKey) {
        const key = this.generateKey(eventKey);
        const now = Date.now();
        const existing = this.eventCache.get(key);
        if (!existing) {
            this.eventCache.set(key, {
                key,
                timestamp: now,
                count: 1,
                lastSeen: now
            });
            return true;
        }
        if (now - existing.timestamp > this.maxAge) {
            this.eventCache.set(key, {
                key,
                timestamp: now,
                count: 1,
                lastSeen: now
            });
            return true;
        }
        if (existing.count >= this.maxDuplicates) {
            logger_js_1.logger.warn(`🚫 Event deduplication: Blocking duplicate event`, {
                eventType: eventKey.type,
                entityId: eventKey.entityId,
                count: existing.count,
                age: now - existing.timestamp
            });
            return false;
        }
        existing.count++;
        existing.lastSeen = now;
        this.eventCache.set(key, existing);
        return true;
    }
    markProcessed(eventKey) {
        const key = this.generateKey(eventKey);
        const existing = this.eventCache.get(key);
        if (existing) {
            existing.lastSeen = Date.now();
            this.eventCache.set(key, existing);
        }
    }
    generateKey(eventKey) {
        const parts = [
            eventKey.type,
            eventKey.entityId,
            eventKey.userId || 'system'
        ];
        return parts.join(':');
    }
    startCleanup() {
        this.cleanupTimer = setInterval(() => {
            this.cleanup();
        }, this.cleanupInterval);
    }
    cleanup() {
        const now = Date.now();
        let cleaned = 0;
        for (const [key, record] of this.eventCache.entries()) {
            if (now - record.lastSeen > this.maxAge * 2) {
                this.eventCache.delete(key);
                cleaned++;
            }
        }
        if (cleaned > 0) {
            logger_js_1.logger.debug(`🧹 Event deduplicator cleaned ${cleaned} old events`);
        }
    }
    getStats() {
        const now = Date.now();
        let oldest = now;
        let newest = 0;
        for (const record of this.eventCache.values()) {
            if (record.timestamp < oldest)
                oldest = record.timestamp;
            if (record.lastSeen > newest)
                newest = record.lastSeen;
        }
        return {
            totalEvents: this.eventCache.size,
            oldestEvent: oldest === now ? 0 : now - oldest,
            newestEvent: newest === 0 ? 0 : now - newest
        };
    }
    clear() {
        this.eventCache.clear();
        logger_js_1.logger.info('🧹 Event deduplicator cache cleared');
    }
    shutdown() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = undefined;
        }
        this.eventCache.clear();
    }
}
exports.EventDeduplicator = EventDeduplicator;
exports.eventDeduplicator = EventDeduplicator.getInstance();

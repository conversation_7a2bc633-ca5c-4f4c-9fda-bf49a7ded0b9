"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BadgeStateManager = void 0;
const logger_1 = require("../utils/logger");
class BadgeStateManager {
    static isResubmissionVoucher(voucher) {
        const hasResubmissionFlag = voucher.is_resubmitted === 1 || voucher.is_resubmitted === true;
        const hasResubmissionStatus = voucher.status === 'RE-SUBMISSION';
        const hasResubmissionWorkflow = voucher.workflow_state?.includes('RESUBMISSION') ||
            voucher.workflow_state === 'AUDIT_NEW_RESUBMITTED';
        const hasRejectionHistory = voucher.rejected_by && voucher.rejected_by.trim() !== '';
        return hasResubmissionFlag || hasResubmissionStatus || hasResubmissionWorkflow || hasRejectionHistory;
    }
    static async setResubmissionState(connection, voucherId, isResubmission, options = {}) {
        const { incrementCount = false, setCertifiedVisibility = false, setAuditVisibility = false, preserveRejectionReason = true } = options;
        try {
            if (isResubmission && preserveRejectionReason) {
                await this.preserveOriginalRejectionReason(connection, voucherId);
            }
            let updateQuery = `
        UPDATE vouchers SET
        is_resubmitted = ?,
        last_resubmission_date = ${isResubmission ? 'NOW()' : 'NULL'}
      `;
            let updateParams = [isResubmission ? 1 : 0];
            if (incrementCount && isResubmission) {
                updateQuery += ', resubmission_count = COALESCE(resubmission_count, 0) + 1';
            }
            if (setCertifiedVisibility) {
                updateQuery += ', resubmission_certified_visible_to_finance = ?';
                updateParams.push(isResubmission ? 1 : 0);
            }
            if (setAuditVisibility) {
                updateQuery += ', resubmission_tracking_visible_to_audit = ?';
                updateParams.push(isResubmission ? 1 : 0);
            }
            updateQuery += ' WHERE id = ?';
            updateParams.push(voucherId);
            await connection.query(updateQuery, updateParams);
            logger_1.logger.info(`🏷️  Badge State: Set resubmission=${isResubmission} for voucher ${voucherId}`);
        }
        catch (error) {
            logger_1.logger.error(`❌ Error setting resubmission state for voucher ${voucherId}:`, error);
            throw error;
        }
    }
    static async preserveOriginalRejectionReason(connection, voucherId) {
        try {
            const [existing] = await connection.query(`
        SELECT voucher_id, original_rejection_reason, original_rejected_by, original_rejection_date,
               rejected_by, rejection_time, comment
        FROM vouchers
        WHERE id = ?
      `, [voucherId]);
            if (existing.length === 0) {
                throw new Error(`Voucher ${voucherId} not found`);
            }
            const voucher = existing[0];
            if (!voucher.original_rejection_reason && voucher.rejected_by) {
                let rejectionReason = voucher.comment;
                if (!rejectionReason || rejectionReason === 'null' || rejectionReason.trim() === '') {
                    logger_1.logger.info(`🔍 Badge State: Main voucher ${voucherId} has no rejection reason, searching copy vouchers...`);
                    const [copyVouchers] = await connection.query(`
            SELECT comment, rejected_by, rejection_time
            FROM vouchers
            WHERE voucher_id LIKE ? AND is_rejection_copy = 1 AND comment IS NOT NULL AND comment != 'null' AND comment != ''
            ORDER BY created_at DESC
            LIMIT 1
          `, [`${voucher.voucher_id}%`]);
                    if (copyVouchers.length > 0) {
                        rejectionReason = copyVouchers[0].comment;
                        logger_1.logger.info(`✅ Badge State: Found rejection reason in copy voucher: "${rejectionReason}"`);
                    }
                }
                await connection.query(`
          UPDATE vouchers SET
          original_rejection_reason = ?,
          original_rejected_by = rejected_by,
          original_rejection_date = COALESCE(rejection_time, NOW()),
          comment = CASE
            WHEN comment IS NULL OR comment = '' OR comment = 'null' OR comment = 'NO COMMENT PROVIDED'
            THEN COALESCE(?, 'Rejected without specific reason')
            ELSE comment
          END
          WHERE id = ?
        `, [
                    rejectionReason || 'Rejected without specific reason',
                    rejectionReason,
                    voucherId
                ]);
                logger_1.logger.info(`🏷️  Badge State: Preserved original rejection reason for voucher ${voucherId}: "${rejectionReason || 'Rejected without specific reason'}"`);
            }
        }
        catch (error) {
            logger_1.logger.error(`❌ Error preserving original rejection reason for voucher ${voucherId}:`, error);
            throw error;
        }
    }
    static async ensureCertifiedResubmissionVisibility(connection, voucherId, isCertified) {
        try {
            const [vouchers] = await connection.query('SELECT is_resubmitted, status FROM vouchers WHERE id = ?', [voucherId]);
            if (vouchers.length === 0) {
                throw new Error(`Voucher ${voucherId} not found`);
            }
            const voucher = vouchers[0];
            const isResubmission = voucher.is_resubmitted === 1;
            if (isResubmission && isCertified) {
                await connection.query(`
          UPDATE vouchers SET 
          resubmission_certified_visible_to_finance = 1,
          resubmission_tracking_visible_to_audit = 1,
          badge_persistence_flags = JSON_SET(
            COALESCE(badge_persistence_flags, '{}'),
            '$.certified_resubmission',
            true,
            '$.persistent_in_dispatched',
            true,
            '$.persistent_in_certified',
            true
          )
          WHERE id = ?
        `, [voucherId]);
                logger_1.logger.info(`🎯 Badge Persistence: Set certified resubmission visibility for voucher ${voucherId}`);
            }
        }
        catch (error) {
            logger_1.logger.error(`❌ Error ensuring certified resubmission visibility for voucher ${voucherId}:`, error);
            throw error;
        }
    }
    static async getBadgeState(connection, voucherId) {
        try {
            const [vouchers] = await connection.query(`
        SELECT
          is_resubmitted,
          resubmission_certified_visible_to_finance,
          resubmission_tracking_visible_to_audit,
          last_resubmission_date,
          resubmission_count,

          badge_persistence_flags
        FROM vouchers
        WHERE id = ?
      `, [voucherId]);
            if (vouchers.length === 0) {
                return null;
            }
            const voucher = vouchers[0];
            return {
                is_resubmitted: voucher.is_resubmitted === 1,
                resubmission_certified_visible_to_finance: voucher.resubmission_certified_visible_to_finance === 1,
                resubmission_tracking_visible_to_audit: voucher.resubmission_tracking_visible_to_audit === 1,
                last_resubmission_date: voucher.last_resubmission_date,
                resubmission_count: voucher.resubmission_count || 0,
                badge_persistence_flags: voucher.badge_persistence_flags
            };
        }
        catch (error) {
            logger_1.logger.error(`❌ Error getting badge state for voucher ${voucherId}:`, error);
            throw error;
        }
    }
    static async clearResubmissionState(connection, voucherId) {
        try {
            await connection.query(`
        UPDATE vouchers SET 
        is_resubmitted = 0,
        resubmission_certified_visible_to_finance = 0,
        resubmission_tracking_visible_to_audit = 0,
        last_resubmission_date = NULL,
        badge_persistence_flags = NULL
        WHERE id = ?
      `, [voucherId]);
            logger_1.logger.info(`🧹 Badge State: Cleared resubmission state for voucher ${voucherId}`);
        }
        catch (error) {
            logger_1.logger.error(`❌ Error clearing resubmission state for voucher ${voucherId}:`, error);
            throw error;
        }
    }
}
exports.BadgeStateManager = BadgeStateManager;

"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.selfHealingNetworkMonitor = exports.SelfHealingNetworkMonitor = void 0;
const events_1 = require("events");
const hybrid_network_service_js_1 = require("./hybrid-network-service.js");
const intelligent_fallback_service_js_1 = require("./intelligent-fallback-service.js");
const network_discovery_service_js_1 = require("./network-discovery-service.js");
const logger_js_1 = require("../utils/logger.js");
class SelfHealingNetworkMonitor extends events_1.EventEmitter {
    _isRunning = false;
    _monitoringInterval = null;
    _healthHistory = [];
    _healingHistory = [];
    _consecutiveFailures = 0;
    _lastHealthCheck = null;
    _startTime = new Date();
    _config = {
        healthCheckInterval: 30000,
        connectivityTimeout: 5000,
        performanceThresholds: {
            maxLatency: 100,
            maxPacketLoss: 5,
            minBandwidth: 1,
            maxResponseTime: 2000
        },
        healingThresholds: {
            consecutiveFailures: 3,
            criticalLatency: 500,
            criticalPacketLoss: 20
        },
        enabled: true
    };
    constructor() {
        super();
        logger_js_1.logger.info('🏥 Self-Healing Network Monitor initialized');
    }
    async start() {
        try {
            if (this._isRunning) {
                logger_js_1.logger.warn('Self-Healing Network Monitor is already running');
                return;
            }
            if (!this._config.enabled) {
                logger_js_1.logger.info('Self-Healing Network Monitor is disabled');
                return;
            }
            logger_js_1.logger.info('🚀 Starting Self-Healing Network Monitor...');
            await this.performHealthCheck();
            this._monitoringInterval = setInterval(this.performHealthCheck.bind(this), this._config.healthCheckInterval);
            this._isRunning = true;
            logger_js_1.logger.info('✅ Self-Healing Network Monitor started successfully');
            logger_js_1.logger.info(`⏰ Health checks every ${this._config.healthCheckInterval / 1000} seconds`);
        }
        catch (error) {
            logger_js_1.logger.error('❌ Failed to start Self-Healing Network Monitor:', error);
            throw error;
        }
    }
    async stop() {
        try {
            if (!this._isRunning) {
                return;
            }
            logger_js_1.logger.info('🛑 Stopping Self-Healing Network Monitor...');
            if (this._monitoringInterval) {
                clearInterval(this._monitoringInterval);
                this._monitoringInterval = null;
            }
            this._isRunning = false;
            logger_js_1.logger.info('✅ Self-Healing Network Monitor stopped');
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error stopping Self-Healing Network Monitor:', error);
        }
    }
    async performHealthCheck() {
        try {
            const startTime = Date.now();
            logger_js_1.logger.debug('🔍 Performing network health check...');
            const metrics = await this.collectHealthMetrics();
            this._healthHistory.push(metrics);
            if (this._healthHistory.length > 1000) {
                this._healthHistory = this._healthHistory.slice(-1000);
            }
            const healingNeeded = this.analyzeHealthMetrics(metrics);
            if (healingNeeded.length > 0) {
                logger_js_1.logger.warn(`⚠️ Network health issues detected: ${healingNeeded.length} problems`);
                await this.performHealingActions(healingNeeded, metrics);
            }
            else {
                this._consecutiveFailures = 0;
                logger_js_1.logger.debug('✅ Network health check passed');
            }
            this._lastHealthCheck = new Date();
            const checkDuration = Date.now() - startTime;
            this.emit('healthCheck', {
                metrics,
                duration: checkDuration,
                healingActions: healingNeeded
            });
        }
        catch (error) {
            logger_js_1.logger.error('❌ Health check failed:', error);
            this._consecutiveFailures++;
            if (this._consecutiveFailures >= this._config.healingThresholds.consecutiveFailures) {
                await this.performEmergencyHealing();
            }
        }
    }
    async collectHealthMetrics() {
        const networkConfig = hybrid_network_service_js_1.hybridNetworkService.getNetworkConfiguration();
        const connectivity = await this.testConnectivity(networkConfig);
        const performance = await this.measurePerformance(networkConfig);
        const stability = this.calculateStabilityMetrics();
        const services = await this.checkServiceHealth();
        return {
            timestamp: new Date(),
            connectivity,
            performance,
            stability,
            services
        };
    }
    async testConnectivity(networkConfig) {
        const results = {
            staticIPReachable: false,
            dynamicIPReachable: false,
            internetReachable: false,
            gatewayReachable: false,
            dnsResolution: false
        };
        try {
            if (networkConfig.staticIP) {
                results.staticIPReachable = await this.pingHost(networkConfig.staticIP);
            }
            if (networkConfig.dynamicIP) {
                results.dynamicIPReachable = await this.pingHost(networkConfig.dynamicIP);
            }
            if (networkConfig.gateway) {
                results.gatewayReachable = await this.pingHost(networkConfig.gateway);
            }
            results.internetReachable = await this.pingHost('*******');
            results.dnsResolution = await this.testDNSResolution();
        }
        catch (error) {
            logger_js_1.logger.debug('Connectivity test error:', error);
        }
        return results;
    }
    async measurePerformance(networkConfig) {
        const performance = {
            latency: 0,
            packetLoss: 0,
            bandwidth: 0,
            responseTime: 0
        };
        try {
            if (networkConfig.gateway) {
                performance.latency = await this.measureLatency(networkConfig.gateway);
            }
            const startTime = Date.now();
            try {
                const response = await fetch(`http://${networkConfig.currentIP}:${networkConfig.port}/health`, {});
                performance.responseTime = Date.now() - startTime;
            }
            catch {
                performance.responseTime = this._config.connectivityTimeout;
            }
            if (networkConfig.gateway) {
                performance.packetLoss = await this.measurePacketLoss(networkConfig.gateway);
            }
        }
        catch (error) {
            logger_js_1.logger.debug('Performance measurement error:', error);
        }
        return performance;
    }
    calculateStabilityMetrics() {
        const recentHistory = this._healthHistory.slice(-20);
        let ipChanges = 0;
        let connectionDrops = 0;
        for (let i = 1; i < recentHistory.length; i++) {
            const prev = recentHistory[i - 1];
            const curr = recentHistory[i];
            if (prev.connectivity.staticIPReachable !== curr.connectivity.staticIPReachable ||
                prev.connectivity.dynamicIPReachable !== curr.connectivity.dynamicIPReachable) {
                ipChanges++;
            }
            if (prev.connectivity.internetReachable && !curr.connectivity.internetReachable) {
                connectionDrops++;
            }
        }
        const uptime = Date.now() - this._startTime.getTime();
        const recoveryTime = this._consecutiveFailures > 0 ? this._consecutiveFailures * this._config.healthCheckInterval : 0;
        return {
            ipChanges,
            connectionDrops,
            recoveryTime,
            uptime
        };
    }
    async checkServiceHealth() {
        return {
            vmsServerHealthy: await this.isVMSServerHealthy(),
            networkDiscoveryActive: network_discovery_service_js_1.networkDiscoveryService.isServiceRunning(),
            fallbackSystemActive: intelligent_fallback_service_js_1.intelligentFallbackService.isRunning()
        };
    }
    analyzeHealthMetrics(metrics) {
        const issues = [];
        if (!metrics.connectivity.internetReachable) {
            issues.push('internet_connectivity');
        }
        if (!metrics.connectivity.gatewayReachable) {
            issues.push('gateway_connectivity');
        }
        if (!metrics.connectivity.dnsResolution) {
            issues.push('dns_resolution');
        }
        if (metrics.performance.latency > this._config.performanceThresholds.maxLatency) {
            issues.push('high_latency');
        }
        if (metrics.performance.packetLoss > this._config.performanceThresholds.maxPacketLoss) {
            issues.push('packet_loss');
        }
        if (metrics.performance.responseTime > this._config.performanceThresholds.maxResponseTime) {
            issues.push('slow_response');
        }
        if (!metrics.services.vmsServerHealthy) {
            issues.push('vms_server_unhealthy');
        }
        if (!metrics.services.networkDiscoveryActive) {
            issues.push('network_discovery_inactive');
        }
        if (!metrics.services.fallbackSystemActive) {
            issues.push('fallback_system_inactive');
        }
        if (metrics.performance.latency > this._config.healingThresholds.criticalLatency) {
            issues.push('critical_latency');
        }
        if (metrics.performance.packetLoss > this._config.healingThresholds.criticalPacketLoss) {
            issues.push('critical_packet_loss');
        }
        return issues;
    }
    async performHealingActions(issues, metrics) {
        logger_js_1.logger.info(`🏥 Performing healing actions for ${issues.length} issues...`);
        for (const issue of issues) {
            try {
                const action = await this.getHealingAction(issue);
                if (action) {
                    const success = await this.executeHealingAction(action);
                    const healingRecord = {
                        id: `healing_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                        type: action.type,
                        description: action.description,
                        severity: action.severity,
                        executedAt: new Date(),
                        success,
                        metrics
                    };
                    this._healingHistory.push(healingRecord);
                    if (this._healingHistory.length > 500) {
                        this._healingHistory = this._healingHistory.slice(-500);
                    }
                    this.emit('healingAction', healingRecord);
                    if (success) {
                        logger_js_1.logger.info(`✅ Healing action successful: ${action.description}`);
                    }
                    else {
                        logger_js_1.logger.warn(`⚠️ Healing action failed: ${action.description}`);
                    }
                }
            }
            catch (error) {
                logger_js_1.logger.error(`❌ Error performing healing action for ${issue}:`, error);
            }
        }
    }
    async getHealingAction(issue) {
        const actions = {
            'internet_connectivity': {
                type: 'switch_mode',
                description: 'Switch network mode due to connectivity issues',
                severity: 'high'
            },
            'gateway_connectivity': {
                type: 'reset_adapter',
                description: 'Reset network adapter due to gateway issues',
                severity: 'high'
            },
            'dns_resolution': {
                type: 'flush_dns',
                description: 'Flush DNS cache due to resolution issues',
                severity: 'medium'
            },
            'high_latency': {
                type: 'switch_mode',
                description: 'Switch network mode due to high latency',
                severity: 'medium'
            },
            'packet_loss': {
                type: 'switch_mode',
                description: 'Switch network mode due to packet loss',
                severity: 'medium'
            },
            'slow_response': {
                type: 'restart_service',
                description: 'Restart VMS server due to slow response',
                severity: 'medium'
            },
            'vms_server_unhealthy': {
                type: 'restart_service',
                description: 'Restart VMS server due to health check failure',
                severity: 'high'
            },
            'network_discovery_inactive': {
                type: 'restart_service',
                description: 'Restart network discovery service',
                severity: 'medium'
            },
            'fallback_system_inactive': {
                type: 'restart_service',
                description: 'Restart intelligent fallback system',
                severity: 'medium'
            },
            'critical_latency': {
                type: 'force_discovery',
                description: 'Force network discovery due to critical latency',
                severity: 'critical'
            },
            'critical_packet_loss': {
                type: 'force_discovery',
                description: 'Force network discovery due to critical packet loss',
                severity: 'critical'
            }
        };
        return actions[issue] || null;
    }
    async executeHealingAction(action) {
        try {
            switch (action.type) {
                case 'restart_service':
                    return await this.restartServices();
                case 'switch_mode':
                    return await this.switchNetworkMode();
                case 'reset_adapter':
                    return await this.resetNetworkAdapter();
                case 'flush_dns':
                    return await this.flushDNSCache();
                case 'force_discovery':
                    return await this.forceNetworkDiscovery();
                default:
                    logger_js_1.logger.warn(`Unknown healing action type: ${action.type}`);
                    return false;
            }
        }
        catch (error) {
            logger_js_1.logger.error(`Error executing healing action ${action.type}:`, error);
            return false;
        }
    }
    async performEmergencyHealing() {
        logger_js_1.logger.warn('🚨 Performing emergency healing due to consecutive failures...');
        try {
            await this.restartServices();
            await new Promise(resolve => setTimeout(resolve, 5000));
            await this.forceNetworkDiscovery();
            await new Promise(resolve => setTimeout(resolve, 5000));
            await this.switchNetworkMode();
            logger_js_1.logger.info('🏥 Emergency healing completed');
            this._consecutiveFailures = 0;
        }
        catch (error) {
            logger_js_1.logger.error('❌ Emergency healing failed:', error);
        }
    }
    async restartServices() {
        try {
            await network_discovery_service_js_1.networkDiscoveryService.stop();
            await intelligent_fallback_service_js_1.intelligentFallbackService.stop();
            await new Promise(resolve => setTimeout(resolve, 2000));
            await network_discovery_service_js_1.networkDiscoveryService.start();
            await intelligent_fallback_service_js_1.intelligentFallbackService.start();
            return true;
        }
        catch {
            return false;
        }
    }
    async switchNetworkMode() {
        try {
            const config = hybrid_network_service_js_1.hybridNetworkService.getNetworkConfiguration();
            if (config.mode === 'static') {
                return await hybrid_network_service_js_1.hybridNetworkService.switchToDynamicModeManual();
            }
            else {
                return await hybrid_network_service_js_1.hybridNetworkService.switchToStaticMode();
            }
        }
        catch {
            return false;
        }
    }
    async resetNetworkAdapter() {
        logger_js_1.logger.info('Network adapter reset requested (not implemented)');
        return true;
    }
    async flushDNSCache() {
        try {
            const { exec } = await Promise.resolve().then(() => __importStar(require('child_process')));
            const { promisify } = await Promise.resolve().then(() => __importStar(require('util')));
            const execAsync = promisify(exec);
            if (process.platform === 'win32') {
                await execAsync('ipconfig /flushdns');
            }
            else {
                await execAsync('sudo systemctl restart systemd-resolved');
            }
            return true;
        }
        catch {
            return false;
        }
    }
    async forceNetworkDiscovery() {
        try {
            await network_discovery_service_js_1.networkDiscoveryService.stop();
            await new Promise(resolve => setTimeout(resolve, 2000));
            await network_discovery_service_js_1.networkDiscoveryService.start();
            return true;
        }
        catch {
            return false;
        }
    }
    async pingHost(host) {
        try {
            const { exec } = await Promise.resolve().then(() => __importStar(require('child_process')));
            const { promisify } = await Promise.resolve().then(() => __importStar(require('util')));
            const execAsync = promisify(exec);
            const command = process.platform === 'win32'
                ? `ping -n 1 -w 1000 ${host}`
                : `ping -c 1 -W 1 ${host}`;
            await execAsync(command);
            return true;
        }
        catch {
            return false;
        }
    }
    async measureLatency(host) {
        const startTime = Date.now();
        const reachable = await this.pingHost(host);
        const endTime = Date.now();
        return reachable ? endTime - startTime : this._config.connectivityTimeout;
    }
    async measurePacketLoss(host) {
        let successful = 0;
        const totalPings = 5;
        for (let i = 0; i < totalPings; i++) {
            if (await this.pingHost(host)) {
                successful++;
            }
        }
        return ((totalPings - successful) / totalPings) * 100;
    }
    async testDNSResolution() {
        try {
            const { lookup } = await Promise.resolve().then(() => __importStar(require('dns')));
            const { promisify } = await Promise.resolve().then(() => __importStar(require('util')));
            const lookupAsync = promisify(lookup);
            await lookupAsync('google.com');
            return true;
        }
        catch {
            return false;
        }
    }
    async isVMSServerHealthy() {
        try {
            const config = hybrid_network_service_js_1.hybridNetworkService.getNetworkConfiguration();
            const response = await fetch(`http://${config.currentIP}:${config.port}/health`, {});
            return response.ok;
        }
        catch {
            return false;
        }
    }
    getStats() {
        return {
            isRunning: this._isRunning,
            uptime: Date.now() - this._startTime.getTime(),
            totalHealthChecks: this._healthHistory.length,
            totalHealingActions: this._healingHistory.length,
            consecutiveFailures: this._consecutiveFailures,
            lastHealthCheck: this._lastHealthCheck,
            recentHealth: this._healthHistory.slice(-10),
            recentHealing: this._healingHistory.slice(-10)
        };
    }
    updateConfig(newConfig) {
        this._config = { ...this._config, ...newConfig };
        logger_js_1.logger.info('Self-Healing Network Monitor configuration updated');
    }
    isRunning() {
        return this._isRunning;
    }
}
exports.SelfHealingNetworkMonitor = SelfHealingNetworkMonitor;
exports.selfHealingNetworkMonitor = new SelfHealingNetworkMonitor();

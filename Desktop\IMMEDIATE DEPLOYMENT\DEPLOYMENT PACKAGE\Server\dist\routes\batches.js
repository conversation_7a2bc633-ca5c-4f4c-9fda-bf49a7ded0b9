"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.batchRouter = void 0;
const express_1 = __importDefault(require("express"));
const uuid_1 = require("uuid");
const db_js_1 = require("../database/db.js");
const auth_js_1 = require("../middleware/auth.js");
const logger_js_1 = require("../utils/logger.js");
const legacy_voucher_status_compat_js_1 = require("../utils/legacy-voucher-status-compat.js");
const simpleEventBus_js_1 = require("../events/simpleEventBus.js");
const badgeStateManager_js_1 = require("../utils/badgeStateManager.js");
const returnStateManager_1 = require("../utils/returnStateManager");
const socketHandlers_js_1 = require("../socket/socketHandlers.js");
exports.batchRouter = express_1.default.Router();
exports.batchRouter.use(auth_js_1.authenticate);
exports.batchRouter.get('/', async (req, res) => {
    try {
        const { department } = req.query;
        let batches;
        if (department) {
            batches = await (0, db_js_1.query)('SELECT * FROM voucher_batches WHERE department = ?', [department]);
        }
        else if (req.user.department === 'AUDIT' || req.user.department === 'SYSTEM ADMIN') {
            batches = await (0, db_js_1.query)('SELECT * FROM voucher_batches WHERE from_audit = 0');
        }
        else {
            batches = await (0, db_js_1.query)('SELECT * FROM voucher_batches WHERE department = ?', [req.user.department]);
        }
        for (const batch of batches) {
            const batchVouchers = await (0, db_js_1.query)(`SELECT v.* FROM vouchers v
         JOIN batch_vouchers bv ON v.id = bv.voucher_id
         WHERE bv.batch_id = ?`, [batch.id]);
            const transformedVouchers = batchVouchers.map((voucher) => ({
                ...voucher,
                voucherId: voucher.voucher_id,
                originalDepartment: voucher.original_department,
                createdBy: voucher.created_by,
                dispatchedBy: voucher.dispatched_by,
                dispatchTime: voucher.dispatch_time,
                sentToAudit: Boolean(voucher.sent_to_audit),
                batchId: voucher.batch_id,
                receivedBy: voucher.received_by,
                receivedByAudit: Boolean(voucher.received_by_audit),
                receiptTime: voucher.receipt_time,
                taxType: voucher.tax_type,
                taxDetails: voucher.tax_details,
                taxAmount: voucher.tax_amount,
                preAuditedAmount: voucher.pre_audited_amount,
                preAuditedBy: voucher.pre_audited_by,
                certifiedBy: voucher.certified_by,
                rejectedBy: voucher.rejected_by,
                rejectionTime: voucher.rejection_time,
                workflowState: voucher.workflow_state,
                isResubmitted: voucher.is_resubmitted,
                isRejectionCopy: voucher.is_rejection_copy,
                resubmissionCertifiedVisibleToFinance: voucher.resubmission_certified_visible_to_finance,
                resubmissionTrackingVisibleToAudit: voucher.resubmission_tracking_visible_to_audit,
                lastResubmissionDate: voucher.last_resubmission_date,
                resubmissionCount: voucher.resubmission_count,
                originalRejectionReason: voucher.original_rejection_reason,
                originalRejectedBy: voucher.original_rejected_by,
                originalRejectionDate: voucher.original_rejection_date
            }));
            batch.vouchers = transformedVouchers;
            batch.voucherIds = transformedVouchers.map((v) => v.id);
        }
        const transformedBatches = batches.map(batch => ({
            ...batch,
            fromAudit: batch.from_audit,
            sentBy: batch.sent_by,
            sentTime: batch.sent_time
        }));
        res.json(transformedBatches);
    }
    catch (error) {
        logger_js_1.logger.error('Get batches error:', error);
        res.status(500).json({ error: 'Failed to get batches' });
    }
});
exports.batchRouter.get('/:id', async (req, res) => {
    const batchId = req.params.id;
    try {
        logger_js_1.logger.info(`Fetching batch: ${batchId}`);
        if (!batchId || batchId.trim() === '') {
            logger_js_1.logger.warn('Invalid batch ID provided');
            return res.status(400).json({ error: 'Invalid batch ID provided' });
        }
        logger_js_1.logger.info('Step 1: Fetching batch from database');
        const batches = await (0, db_js_1.query)('SELECT * FROM voucher_batches WHERE id = ?', [batchId]);
        if (batches.length === 0) {
            logger_js_1.logger.warn(`Batch not found: ${batchId}`);
            return res.status(404).json({
                error: 'Batch not found',
                message: `Batch ${batchId} does not exist or has been removed`,
                batchId: batchId
            });
        }
        const batch = batches[0];
        logger_js_1.logger.info(`Step 2: Batch found - ${batch.department} from ${batch.sent_by}`);
        if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN' && batch.department !== req.user.department) {
            logger_js_1.logger.warn(`Access denied for user ${req.user.name} to batch ${batchId}`);
            return res.status(403).json({ error: 'Access denied' });
        }
        logger_js_1.logger.info('Step 3: Fetching vouchers in batch');
        const batchVouchers = await (0, db_js_1.query)(`SELECT v.* FROM vouchers v
       JOIN batch_vouchers bv ON v.id = bv.voucher_id
       WHERE bv.batch_id = ?`, [batchId]);
        logger_js_1.logger.info(`Step 4: Found ${batchVouchers.length} vouchers in batch`);
        const transformedVouchers = batchVouchers.map((voucher) => ({
            ...voucher,
            voucherId: voucher.voucher_id,
            originalDepartment: voucher.original_department,
            createdBy: voucher.created_by,
            dispatchedBy: voucher.dispatched_by,
            dispatchTime: voucher.dispatch_time,
            sentToAudit: Boolean(voucher.sent_to_audit),
            batchId: voucher.batch_id,
            receivedBy: voucher.received_by,
            receivedByAudit: Boolean(voucher.received_by_audit),
            receiptTime: voucher.receipt_time,
            taxType: voucher.tax_type,
            taxDetails: voucher.tax_details,
            taxAmount: voucher.tax_amount,
            preAuditedAmount: voucher.pre_audited_amount,
            preAuditedBy: voucher.pre_audited_by,
            certifiedBy: voucher.certified_by,
            rejectedBy: voucher.rejected_by,
            rejectionTime: voucher.rejection_time,
            comment: voucher.comment,
            workflowState: voucher.workflow_state,
            isResubmitted: voucher.is_resubmitted,
            isRejectionCopy: voucher.is_rejection_copy,
            resubmissionCertifiedVisibleToFinance: voucher.resubmission_certified_visible_to_finance,
            resubmissionTrackingVisibleToAudit: voucher.resubmission_tracking_visible_to_audit,
            lastResubmissionDate: voucher.last_resubmission_date,
            resubmissionCount: voucher.resubmission_count,
            originalRejectionReason: voucher.original_rejection_reason,
            originalRejectedBy: voucher.original_rejected_by,
            originalRejectionDate: voucher.original_rejection_date
        }));
        batch.vouchers = transformedVouchers;
        batch.voucherIds = transformedVouchers.map((v) => v.id);
        batch.voucherCount = transformedVouchers.length;
        batch.hasVouchers = transformedVouchers.length > 0;
        logger_js_1.logger.info(`Batch ${batchId} fetched successfully with ${transformedVouchers.length} vouchers`);
        res.json(batch);
    }
    catch (error) {
        logger_js_1.logger.error('Get batch error:', error);
        res.status(500).json({
            error: 'Failed to get batch',
            message: 'An internal server error occurred while fetching the batch',
            batchId: batchId
        });
    }
});
exports.batchRouter.post('/', async (req, res) => {
    const connection = await (0, db_js_1.getTransaction)();
    try {
        const { department, voucherIds, fromAudit = false, dispatchedBy } = req.body;
        console.log(`🔍 DISPATCHER DEBUG: Received in batch API - dispatchedBy: "${dispatchedBy}"`);
        console.log(`🔍 DISPATCHER DEBUG: Current user: "${req.user.name}"`);
        console.log(`🔍 DISPATCHER DEBUG: fromAudit: ${fromAudit}`);
        if (!department || !voucherIds || !Array.isArray(voucherIds) || voucherIds.length === 0) {
            return res.status(400).json({ error: 'Department and voucher IDs are required' });
        }
        if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN' && department !== req.user.department) {
            return res.status(403).json({ error: 'Access denied' });
        }
        for (const voucherId of voucherIds) {
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
            if (!uuidRegex.test(voucherId)) {
                await connection.rollback();
                return res.status(400).json({
                    error: `Invalid voucher ID format: ${voucherId}. Expected UUID format.`,
                    details: 'Voucher IDs must be valid UUIDs. If you see timestamp-based IDs (e.g., v1234567890), this indicates a system configuration issue.'
                });
            }
            const vouchers = await connection.query('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [voucherId]);
            if (vouchers[0].length === 0) {
                await connection.rollback();
                return res.status(404).json({
                    error: `Voucher with ID ${voucherId} not found`,
                    details: 'The voucher may have been deleted or the ID is incorrect. Please refresh the page and try again.'
                });
            }
            const voucher = vouchers[0][0];
            if (voucher.department !== department && !fromAudit) {
                await connection.rollback();
                return res.status(400).json({ error: `Voucher with ID ${voucherId} does not belong to department ${department}` });
            }
        }
        let normalVoucherCount = 0;
        let rejectedVoucherCount = 0;
        let resubmissionCount = 0;
        for (const voucherId of voucherIds) {
            const [voucherCheck] = await connection.query('SELECT status, rejected_by, rejection_time FROM vouchers WHERE id = ?', [voucherId]);
            if (voucherCheck[0]) {
                const voucher = voucherCheck[0];
                const isResubmission = (voucher.rejected_by && voucher.rejected_by.trim() !== '') ||
                    (voucher.rejection_time) ||
                    (voucher.status === 'VOUCHER REJECTED');
                if (voucher.status === 'VOUCHER REJECTED') {
                    rejectedVoucherCount++;
                }
                else if (isResubmission) {
                    resubmissionCount++;
                }
                else {
                    normalVoucherCount++;
                }
            }
        }
        const containsRejectedVouchers = rejectedVoucherCount > 0;
        const containsResubmissions = resubmissionCount > 0;
        console.log(`📦 ENHANCED BATCH COMPOSITION: ${normalVoucherCount} normal + ${rejectedVoucherCount} rejected + ${resubmissionCount} resubmissions`);
        const batchId = (0, uuid_1.v4)();
        const actualDispatcher = dispatchedBy || req.user.name;
        console.log(`🔍 DISPATCHER DEBUG: Final actualDispatcher will be: "${actualDispatcher}"`);
        await connection.query(`INSERT INTO voucher_batches (
        id, department, sent_by, sent_time, received, from_audit,
        contains_rejected_vouchers, rejected_voucher_count,
        contains_resubmissions, resubmission_count
      ) VALUES (?, ?, ?, NOW(), ?, ?, ?, ?, ?, ?)`, [
            batchId, department, actualDispatcher, false, fromAudit,
            containsRejectedVouchers, rejectedVoucherCount,
            containsResubmissions, resubmissionCount
        ]);
        logger_js_1.logger.info(`📦 Batch created: ${batchId} - Contains ${rejectedVoucherCount} rejected + ${resubmissionCount} resubmission voucher(s)`);
        for (const voucherId of voucherIds) {
            await connection.query('INSERT INTO batch_vouchers (batch_id, voucher_id) VALUES (?, ?)', [batchId, voucherId]);
            const currentVoucherQuery = await connection.query('SELECT status FROM vouchers WHERE id = ?', [voucherId]);
            const currentVoucherRows = currentVoucherQuery[0];
            const currentVoucher = currentVoucherRows && currentVoucherRows.length > 0 ? currentVoucherRows[0] : null;
            if (!currentVoucher) {
                logger_js_1.logger.error(`❌ Voucher ${voucherId} not found during batch dispatch`);
                continue;
            }
            let newStatus;
            let updateQuery;
            let updateParams;
            if (fromAudit && currentVoucher.status === legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED) {
                newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED;
                const { flags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({ status: newStatus });
                updateQuery = `
          UPDATE vouchers SET
            batch_id = ?,
            status = ?,
            flags = ?,
            audit_dispatch_time = NOW(),
            audit_dispatched_by = ?,
            department = ?,
            dispatched = 0
          WHERE id = ?
        `;
                updateParams = [batchId, newStatus, JSON.stringify(flags), actualDispatcher, department, voucherId];
                logger_js_1.logger.info(`📤 REJECTED VOUCHER: Dispatching ${voucherId} FROM AUDIT TO ${department} REJECTED tab - Status: ${newStatus}`);
            }
            else if (fromAudit) {
                newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_CERTIFIED;
                const { flags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({ status: newStatus });
                if (currentVoucher.is_returned_voucher === 1) {
                    const isResubmission = (currentVoucher.is_resubmitted === 1 ||
                        currentVoucher.is_resubmitted === true ||
                        currentVoucher.resubmission_count > 0 ||
                        currentVoucher.last_resubmission_date ||
                        (currentVoucher.certified_by && currentVoucher.certified_by.trim() !== '') ||
                        (currentVoucher.certified_amount && currentVoucher.certified_amount > 0));
                    const isPhase2CertifiedReturn = (currentVoucher.is_returned_voucher === 1 &&
                        isResubmission &&
                        ((currentVoucher.certified_by && currentVoucher.certified_by.trim() !== '') ||
                            (currentVoucher.certified_amount && currentVoucher.certified_amount > 0)));
                    if (isPhase2CertifiedReturn) {
                        newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_CERTIFIED;
                        const { flags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({ status: newStatus });
                        updateQuery = `UPDATE vouchers SET
              batch_id = ?,
              status = ?,
              flags = ?,
              workflow_state = 'AUDIT_DISPATCHED',
              audit_dispatch_time = NOW(),
              audit_dispatched_by = ?,
              original_department = ?,
              department = ?,
              dispatched = 1
              WHERE id = ?`;
                        updateParams = [batchId, newStatus, JSON.stringify(flags), actualDispatcher, department, department, voucherId];
                        logger_js_1.logger.info(`📤 PHASE 2 CERTIFIED RETURNED VOUCHER: Dispatching ${voucherId} FROM AUDIT TO ${department} - Status: ${newStatus} (CERTIFIED for dual visibility), Workflow: AUDIT_DISPATCHED`);
                    }
                    else {
                        newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_RETURNED;
                        const { flags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({ status: newStatus });
                        updateQuery = `UPDATE vouchers SET
              batch_id = ?,
              status = ?,
              flags = ?,
              workflow_state = 'AUDIT_DISPATCHED',
              audit_dispatch_time = NOW(),
              audit_dispatched_by = ?,
              original_department = ?,
              department = ?,
              dispatched = 1
              WHERE id = ?`;
                        updateParams = [batchId, newStatus, JSON.stringify(flags), actualDispatcher, department, department, voucherId];
                        logger_js_1.logger.info(`📤 PHASE 1 RETURNED VOUCHER: Dispatching ${voucherId} FROM AUDIT TO ${department} - Status: ${newStatus} (STRICTLY RETURNED), Workflow: AUDIT_DISPATCHED`);
                    }
                }
                else {
                    updateQuery = `UPDATE vouchers SET
            batch_id = ?,
            status = ?,
            flags = ?,
            workflow_state = 'AUDIT_DISPATCHED',
            audit_dispatch_time = NOW(),
            audit_dispatched_by = ?,
            original_department = ?,
            department = ?,
            dispatched = 1
            WHERE id = ?`;
                    updateParams = [batchId, newStatus, JSON.stringify(flags), actualDispatcher, department, department, voucherId];
                    logger_js_1.logger.info(`📤 NORMAL: Dispatching ${voucherId} FROM AUDIT TO ${department} - Status: ${newStatus}, Workflow: AUDIT_DISPATCHED`);
                }
            }
            else {
                const [fullVoucherQuery] = await connection.query('SELECT * FROM vouchers WHERE id = ?', [voucherId]);
                const voucher = fullVoucherQuery[0];
                const condition1 = voucher.rejected_by && voucher.rejected_by.trim() !== '';
                const condition2 = voucher.rejection_time;
                const condition3 = voucher.status === 'VOUCHER REJECTED';
                logger_js_1.logger.info(`🔍 RESUBMISSION DEBUG - Voucher ${voucher.voucher_id}:`, {
                    voucherId: voucher.voucher_id,
                    status: voucher.status,
                    rejected_by: voucher.rejected_by || 'NULL',
                    rejection_time: voucher.rejection_time || 'NULL',
                    comment: voucher.comment || 'NULL'
                });
                logger_js_1.logger.info(`🧪 RESUBMISSION CONDITIONS - ${voucher.voucher_id}:`, {
                    condition1_rejected_by: condition1,
                    condition2_rejection_time: condition2,
                    condition3_status_rejected: condition3,
                });
                const isResubmission = condition1 || condition2 || condition3;
                logger_js_1.logger.info(`🎯 RESUBMISSION RESULT - ${voucher.voucher_id}: ${isResubmission ? 'YES' : 'NO'}`);
                if (isResubmission) {
                    newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.PENDING_RECEIPT;
                    const workflowState = 'FINANCE_PROCESSING';
                    const { flags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({ status: newStatus });
                    updateQuery = `UPDATE vouchers SET
            batch_id = ?, status = ?, workflow_state = ?, flags = ?,
            sent_to_audit = TRUE, dispatch_to_audit_by = ?, dispatch_time = NOW(),
            is_resubmitted = TRUE, department = 'FINANCE'
            WHERE id = ?`;
                    updateParams = [batchId, newStatus, workflowState, JSON.stringify(flags), actualDispatcher, voucherId];
                    logger_js_1.logger.info(`🔄 RESUBMISSION: Sending ${voucherId} FROM ${department} TO AUDIT - Status: ${newStatus}, Workflow: ${workflowState}`);
                }
                else {
                    newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.PENDING_RECEIPT;
                    const { flags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({ status: newStatus });
                    updateQuery = 'UPDATE vouchers SET batch_id = ?, status = ?, flags = ?, sent_to_audit = TRUE, dispatch_to_audit_by = ?, dispatch_time = NOW() WHERE id = ?';
                    updateParams = [batchId, newStatus, JSON.stringify(flags), actualDispatcher, voucherId];
                    logger_js_1.logger.info(`📥 NORMAL: Sending ${voucherId} FROM ${department} TO AUDIT - Status: ${newStatus}`);
                }
            }
            await connection.query(updateQuery, updateParams);
        }
        const targetDepartment = fromAudit ? department : 'AUDIT';
        let notificationMessage;
        if (fromAudit) {
            if (rejectedVoucherCount > 0) {
                const totalVouchers = voucherIds.length;
                const acceptedCount = totalVouchers - rejectedVoucherCount;
                if (rejectedVoucherCount === totalVouchers) {
                    notificationMessage = `Batch received from Audit: ${rejectedVoucherCount} rejected voucher${rejectedVoucherCount > 1 ? 's' : ''}`;
                }
                else {
                    notificationMessage = `Batch received from Audit: ${acceptedCount} voucher${acceptedCount > 1 ? 's' : ''}, ${rejectedVoucherCount} rejected`;
                }
            }
            else {
                notificationMessage = `New batch received from Audit`;
            }
        }
        else {
            notificationMessage = `New batch received from ${department}`;
        }
        logger_js_1.logger.info(`📤 Creating individual notifications for ${targetDepartment} department users`);
        const [departmentUsers] = await connection.query('SELECT id, name FROM users WHERE department = ? AND is_active = 1', [targetDepartment]);
        if (!Array.isArray(departmentUsers) || departmentUsers.length === 0) {
            logger_js_1.logger.warn(`⚠️ No active users found in ${targetDepartment} department`);
            return;
        }
        const notificationPromises = departmentUsers.map(async (user) => {
            const notificationId = (0, uuid_1.v4)();
            await connection.query(`INSERT INTO notifications (
          id, user_id, message, is_read, timestamp, batch_id, type, from_audit
        ) VALUES (?, ?, ?, ?, NOW(), ?, ?, ?)`, [
                notificationId,
                user.id,
                notificationMessage,
                false,
                batchId,
                'NEW_BATCH',
                fromAudit
            ]);
            return { notificationId, userId: user.id, userName: user.name };
        });
        const createdNotifications = await Promise.all(notificationPromises);
        logger_js_1.logger.info(`✅ Created ${createdNotifications.length} individual notifications for ${targetDepartment} department users`);
        const notificationData = {
            batchId,
            department: fromAudit ? 'AUDIT' : department,
            voucherCount: voucherIds.length,
            sentBy: actualDispatcher,
            timestamp: Date.now(),
            message: notificationMessage,
            notifications: createdNotifications.map((n) => ({
                id: n.notificationId,
                userId: n.userId,
                userName: n.userName,
                status: 'pending'
            }))
        };
        if (!fromAudit && targetDepartment === 'AUDIT') {
            (0, socketHandlers_js_1.broadcastToAuditDepartment)('new_batch_notification', notificationData);
            logger_js_1.logger.info(`📢 REAL-TIME: Broadcasted new_batch_notification to Audit department for batch ${batchId} (${createdNotifications.length} users)`);
        }
        else if (fromAudit && targetDepartment !== 'AUDIT') {
            (0, socketHandlers_js_1.broadcastToDepartment)(targetDepartment, 'new_batch_notification', notificationData);
            logger_js_1.logger.info(`📢 REAL-TIME: Broadcasted new_batch_notification to ${targetDepartment} department for batch ${batchId} (${createdNotifications.length} users)`);
        }
        logger_js_1.logger.info(`✅ Batch ${batchId} completed with metadata: ${normalVoucherCount} normal, ${rejectedVoucherCount} rejected`);
        await connection.commit();
        const batches = await (0, db_js_1.query)('SELECT * FROM voucher_batches WHERE id = ?', [batchId]);
        const batchVouchers = await (0, db_js_1.query)(`SELECT v.* FROM vouchers v
       JOIN batch_vouchers bv ON v.id = bv.voucher_id
       WHERE bv.batch_id = ?`, [batchId]);
        const result = batches[0];
        result.vouchers = batchVouchers;
        result.voucherIds = batchVouchers.map((v) => v.id);
        simpleEventBus_js_1.simpleEventBus.emitBatchCreated(result);
        logger_js_1.logger.info(`📡 Emitted batch creation event for ${batchId}`);
        logger_js_1.logger.info(`✅ Successfully processed batch ${batchId} with ${batchVouchers.length} vouchers`);
        res.status(201).json(result);
    }
    catch (error) {
        await connection.rollback();
        logger_js_1.logger.error('Create batch error:', error);
        res.status(500).json({ error: 'Failed to create batch' });
    }
    finally {
        connection.release();
    }
});
exports.batchRouter.post('/:id/receive', async (req, res) => {
    const startTime = Date.now();
    const batchId = req.params.id;
    logger_js_1.logger.info(`🔄 BATCH RECEIVE: Starting batch receive for ${batchId} by ${req.user.name}`);
    try {
        const { receivedVoucherIds = [], rejectedVoucherIds = [], rejectionComments = {} } = req.body;
        logger_js_1.logger.info(`🔄 BATCH RECEIVE: Processing ${receivedVoucherIds.length} accepted, ${rejectedVoucherIds.length} rejected vouchers`);
        const result = await Promise.race([
            (0, db_js_1.withTransaction)(async (connection) => {
                logger_js_1.logger.info(`🔄 BATCH RECEIVE: Transaction started for batch ${batchId}`);
                logger_js_1.logger.info(`🔄 BATCH RECEIVE: Fetching batch details...`);
                const [batches] = await connection.execute('SELECT * FROM voucher_batches WHERE id = ?', [batchId]);
                if (batches.length === 0) {
                    throw new Error('Batch not found');
                }
                logger_js_1.logger.info(`🔄 BATCH RECEIVE: Batch found, proceeding with processing...`);
                const receiveBatch = batches[0];
                const isFromAudit = receiveBatch.from_audit;
                if (isFromAudit) {
                    if (req.user.department !== receiveBatch.department && req.user.department !== 'SYSTEM ADMIN') {
                        throw new Error('Access denied');
                    }
                }
                else {
                    if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN') {
                        throw new Error('Access denied');
                    }
                }
                await connection.execute('UPDATE voucher_batches SET received = TRUE, received_by = ? WHERE id = ?', [req.user.name, batchId]);
                await connection.execute('UPDATE notifications SET is_read = TRUE WHERE batch_id = ? AND type = ?', [batchId, 'NEW_BATCH']);
                logger_js_1.logger.info(`🔒 BATCH CLAIMED: Batch ${batchId} claimed by ${req.user.name}, notifications marked as read for all users`);
                (0, socketHandlers_js_1.broadcastToAuditDepartment)('batch_claimed', {
                    batchId,
                    claimedBy: req.user.name,
                    claimedById: req.user.id,
                    timestamp: Date.now(),
                    message: `Batch ${batchId} is now being processed by ${req.user.name}`
                });
                logger_js_1.logger.info(`📢 REAL-TIME: Broadcasted batch claim notification to all audit users`);
                const [batchVouchers] = await connection.execute(`SELECT v.* FROM vouchers v
         JOIN batch_vouchers bv ON v.id = bv.voucher_id
         WHERE bv.batch_id = ?`, [batchId]);
                const allVoucherIds = batchVouchers.map((v) => v.id);
                for (const voucherId of receivedVoucherIds) {
                    if (!allVoucherIds.includes(voucherId)) {
                        continue;
                    }
                    const [currentVoucher] = await connection.query('SELECT * FROM vouchers WHERE id = ? FOR UPDATE', [voucherId]);
                    if (!currentVoucher[0]) {
                        continue;
                    }
                    const voucher = currentVoucher[0];
                    let newStatus;
                    if (isFromAudit) {
                        const isResubmission = (voucher.is_resubmitted === 1 ||
                            voucher.is_resubmitted === true ||
                            voucher.resubmission_count > 0 ||
                            voucher.last_resubmission_date ||
                            (voucher.certified_by && voucher.certified_by.trim() !== '') ||
                            (voucher.certified_amount && voucher.certified_amount > 0));
                        if (isResubmission) {
                            const isResubmittedAndCertified = ((voucher.certified_by && voucher.certified_by.trim() !== '') ||
                                (voucher.certified_amount && voucher.certified_amount > 0));
                            if (isResubmittedAndCertified) {
                                newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_CERTIFIED;
                                const isReturnedResubmission = voucher.is_returned_voucher === 1 || voucher.returned_by;
                                if (isReturnedResubmission) {
                                    logger_js_1.logger.info(`🎯 PHASE 2 CERTIFIED RETURNED RESUBMISSION: Setting CERTIFIED status for voucher ${voucherId} (returned by ${voucher.returned_by}, certified by ${voucher.certified_by}, amount: ${voucher.certified_amount})`);
                                }
                                else {
                                    logger_js_1.logger.info(`🔄 CERTIFIED RESUBMISSION: Setting CERTIFIED status for voucher ${voucherId} (certified by ${voucher.certified_by}, amount: ${voucher.certified_amount})`);
                                }
                            }
                            else {
                                newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_PROCESSING;
                                logger_js_1.logger.info(`🔄 PENDING RESUBMISSION: Setting PROCESSING status for voucher ${voucherId} (resubmitted but not yet certified)`);
                            }
                        }
                        else if (voucher.is_rejection_copy && voucher.rejected_by && voucher.rejection_time) {
                            newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED;
                            logger_js_1.logger.info(`🔄 REJECTION COPY: Setting REJECTED status for copy voucher ${voucherId} (rejected by ${voucher.rejected_by})`);
                        }
                        else if (voucher.returned_by && voucher.return_time && !voucher.is_returned_copy) {
                            newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_RETURNED;
                            logger_js_1.logger.info(`🔄 PHASE 1 RETURNED VOUCHER: Strictly setting RETURNED status for voucher ${voucherId} (returned by ${voucher.returned_by})`);
                        }
                        else if (voucher.rejected_by && voucher.rejection_time && !voucher.is_rejection_copy) {
                            const isResubmittedAndCertified = (voucher.is_resubmitted === 1 || voucher.is_resubmitted === true) &&
                                (voucher.certified_by && voucher.certified_by.trim() !== '');
                            if (isResubmittedAndCertified) {
                                logger_js_1.logger.info(`🔄 RESUBMISSION CERTIFIED: Voucher ${voucherId} is a certified resubmission - processing as certified despite rejection history`);
                                newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_CERTIFIED;
                            }
                            else {
                                newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED;
                                logger_js_1.logger.info(`🔄 PERMANENT REJECTED: Restoring REJECTED status for voucher ${voucherId} (rejected by ${voucher.rejected_by})`);
                            }
                        }
                        else {
                            newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_CERTIFIED;
                        }
                    }
                    else {
                        newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.AUDIT_PROCESSING;
                    }
                    const { flags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({ status: newStatus });
                    let currentFlags = {};
                    if (voucher.flags) {
                        try {
                            if (typeof voucher.flags === 'object') {
                                currentFlags = voucher.flags;
                            }
                            else {
                                currentFlags = JSON.parse(voucher.flags);
                            }
                        }
                        catch (error) {
                            logger_js_1.logger.warn(`Invalid flags JSON for voucher ${voucherId}: ${voucher.flags}. Using empty flags.`);
                            currentFlags = {};
                        }
                    }
                    logger_js_1.logger.info(`🔍 VALIDATION DEBUG: Checking transition for voucher ${voucherId}:`, {
                        currentStatus: voucher.status,
                        newStatus: newStatus,
                        userRole: req.user.role,
                        userDepartment: req.user.department,
                        userName: req.user.name,
                        currentFlags: currentFlags
                    });
                    logger_js_1.logger.info(`🔄 BATCH RECEIVE: Bypassing status validation for system operation ${voucher.status} -> ${newStatus}`, {
                        userRole: req.user.role,
                        userDepartment: req.user.department,
                        userName: req.user.name,
                        voucherId: voucher.id
                    });
                    logger_js_1.logger.info(`🔄 AUDIT ACCEPTANCE: Changing voucher ${voucherId} status: ${voucher.status} → ${newStatus} (received by ${req.user.name})`);
                    logger_js_1.logger.info(`🔄 AUDIT ACCEPTANCE: Setting received_by_audit = ${!isFromAudit} for voucher ${voucherId}`);
                    if (isFromAudit) {
                        const currentVoucherData = currentVoucher[0];
                        if (currentVoucherData.reference_id) {
                            logger_js_1.logger.info(`🔄 OFFSET LOGIC: Processing voucher ${voucherId} with reference_id ${currentVoucherData.reference_id}`);
                            const [originalVouchers] = await connection.query(`SELECT id, status FROM vouchers
             WHERE voucher_id = ? AND department = ? AND status IN ('PENDING', 'VOUCHER PROCESSING')`, [currentVoucherData.reference_id, currentVoucherData.original_department || currentVoucherData.department]);
                            if (originalVouchers.length > 0) {
                                const originalVoucher = originalVouchers[0];
                                logger_js_1.logger.info(`🔄 OFFSET LOGIC: Found original voucher ${originalVoucher.id} with status ${originalVoucher.status} - removing from Processing tab`);
                                await connection.query(`UPDATE vouchers SET
               status = 'OFFSET_BY_AUDIT',
               deleted = TRUE,
               deletion_time = NOW()
               WHERE id = ?`, [originalVoucher.id]);
                                logger_js_1.logger.info(`✅ OFFSET LOGIC: Original voucher ${originalVoucher.id} offset by processed voucher ${voucherId}`);
                            }
                            else {
                                logger_js_1.logger.warn(`⚠️ OFFSET LOGIC: No original voucher found for reference_id ${currentVoucherData.reference_id}`);
                            }
                        }
                        const targetDepartment = newStatus === legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED
                            ? req.user.department
                            : (voucher.original_department || voucher.department);
                        let finalStatus = newStatus;
                        let finalWorkflowState = 'FINANCE_PROCESSING';
                        let isResubmittedVoucher = false;
                        if (newStatus === legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED) {
                            finalStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED;
                            finalWorkflowState = 'FINANCE_REJECTED';
                        }
                        else if (newStatus === legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_RETURNED) {
                            finalStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_RETURNED;
                            finalWorkflowState = 'FINANCE_RETURNED';
                        }
                        else {
                            isResubmittedVoucher = voucher.is_resubmitted === 1 || voucher.is_resubmitted === true;
                        }
                        if (isResubmittedVoucher) {
                            finalStatus = newStatus;
                            if (targetDepartment === 'AUDIT') {
                                finalWorkflowState = 'AUDIT_NEW_RESUBMITTED';
                            }
                            else {
                                if (finalStatus === legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_CERTIFIED) {
                                    finalWorkflowState = 'FINANCE_CERTIFIED';
                                }
                                else {
                                    finalWorkflowState = 'FINANCE_PROCESSING';
                                }
                            }
                        }
                        logger_js_1.logger.info(`🔄 FINANCE RECEIVING: Voucher ${voucherId} - isResubmitted: ${isResubmittedVoucher}, finalStatus: ${finalStatus}, workflow_state: ${finalWorkflowState}`);
                        const shouldKeepAuditVisibility = false;
                        const actualWorkflowState = finalWorkflowState;
                        if (isResubmittedVoucher) {
                            await badgeStateManager_js_1.BadgeStateManager.setResubmissionState(connection, voucherId, true, {
                                setCertifiedVisibility: shouldKeepAuditVisibility,
                                setAuditVisibility: shouldKeepAuditVisibility
                            });
                            if (shouldKeepAuditVisibility) {
                                await badgeStateManager_js_1.BadgeStateManager.ensureCertifiedResubmissionVisibility(connection, voucherId, true);
                            }
                            const isReturnedResubmission = voucher.is_returned_voucher === 1 || voucher.is_returned_voucher === true;
                            if (isReturnedResubmission) {
                                await returnStateManager_1.ReturnStateManager.setReturnState(connection, voucherId, true, {
                                    preserveReturnReason: true,
                                    setCertifiedVisibility: shouldKeepAuditVisibility
                                });
                                logger_js_1.logger.info(`🔄 RETURNED VOUCHER RESUBMISSION: Set return state for voucher ${voucherId}`);
                            }
                        }
                        const isResubmission = (voucher.is_resubmitted === 1 ||
                            voucher.is_resubmitted === true ||
                            voucher.resubmission_count > 0 ||
                            voucher.last_resubmission_date ||
                            (voucher.certified_by && voucher.certified_by.trim() !== '') ||
                            (voucher.certified_amount && voucher.certified_amount > 0));
                        const isPhase2CertifiedReturn = (voucher.is_returned_voucher === 1 &&
                            isResubmission &&
                            ((voucher.certified_by && voucher.certified_by.trim() !== '') ||
                                (voucher.certified_amount && voucher.certified_amount > 0)));
                        if (isPhase2CertifiedReturn) {
                            await connection.query(`
            UPDATE vouchers SET
            return_certified_visible_to_finance = 1,
            resubmission_certified_visible_to_finance = 1,
            badge_persistence_flags = JSON_SET(
              COALESCE(badge_persistence_flags, '{}'),
              '$.certified_returned', true,
              '$.certified_resubmission', true,
              '$.resubmitted_return', true,
              '$.persistent_in_dispatched', true,
              '$.persistent_in_certified', true,
              '$.dual_tab_visibility', true,
              '$.phase_2_complete', true
            )
            WHERE id = ?
          `, [voucherId]);
                            logger_js_1.logger.info(`🎯 PHASE 2 DUAL VISIBILITY: Enabled dual tab visibility for certified returned voucher ${voucherId}`);
                        }
                        let updateQuery = `UPDATE vouchers SET
           status = ?,
           workflow_state = ?,
           flags = ?,
           department = ?,
           received_by = ?,
           receipt_time = NOW(),
           department_receipt_time = NOW(),
           department_received_by = ?,
           finance_received = ?`;
                        let updateParams = [finalStatus, actualWorkflowState, JSON.stringify(flags), targetDepartment, req.user.name, req.user.name, shouldKeepAuditVisibility ? 1 : 0];
                        if (finalStatus !== legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_RETURNED) {
                            updateQuery += `, comment = ?`;
                            updateParams.push('NO COMMENT PROVIDED');
                        }
                        updateQuery += ` WHERE id = ?`;
                        updateParams.push(voucherId);
                        await connection.query(updateQuery, updateParams);
                        logger_js_1.logger.info(`🔄 AUDIT VISIBILITY: Voucher ${voucherId} - keepAuditVisibility: ${shouldKeepAuditVisibility}, actualWorkflowState: ${actualWorkflowState}`);
                    }
                    else {
                        const isResubmittedVoucher = voucher.status === 'RE-SUBMISSION' ||
                            voucher.is_resubmitted ||
                            (voucher.rejected_by && voucher.rejected_by.trim() !== '') ||
                            voucher.rejection_time;
                        const auditWorkflowState = isResubmittedVoucher ? 'AUDIT_NEW_RESUBMITTED' : 'AUDIT_NEW';
                        logger_js_1.logger.info(`🔄 AUDIT RECEIVING: Voucher ${voucherId} - isResubmitted: ${isResubmittedVoucher}, workflow_state: ${auditWorkflowState}`);
                        await connection.query(`UPDATE vouchers SET
           status = ?,
           flags = ?,
           department = 'AUDIT',
           original_department = COALESCE(original_department, department),
           received_by = ?,
           receipt_time = NOW(),
           received_by_audit = TRUE,
           pre_audited_amount = NULL,
           pre_audited_by = NULL,
           certified_by = NULL,
           work_started = FALSE,
           comment = NULL,
           workflow_state = ?
           WHERE id = ?`, [newStatus, JSON.stringify(flags), req.user.name, auditWorkflowState, voucherId]);
                        const updatedVoucherResult = await connection.query('SELECT * FROM vouchers WHERE id = ?', [voucherId]);
                        if (updatedVoucherResult.length > 0) {
                            const updatedVoucher = updatedVoucherResult[0];
                            (0, socketHandlers_js_1.broadcastVoucherUpdate)('status_changed', {
                                id: updatedVoucher.id,
                                voucher_id: updatedVoucher.voucher_id,
                                status: updatedVoucher.status,
                                department: updatedVoucher.department,
                                original_department: updatedVoucher.original_department,
                                workflow_state: updatedVoucher.workflow_state,
                                received_by: updatedVoucher.received_by,
                                receipt_time: updatedVoucher.receipt_time,
                                previousStatus: 'PENDING RECEIPT',
                                newStatus: updatedVoucher.status,
                                statusChangeType: 'receipt_processing'
                            });
                            logger_js_1.logger.info(`📢 REAL-TIME: Broadcasted voucher status update for ${updatedVoucher.voucher_id}: ${newStatus} (received by Audit)`);
                        }
                    }
                    logger_js_1.logger.info(`✅ AUDIT PROCESSING: Successfully updated voucher ${voucherId} with status ${newStatus}`);
                    if (!isFromAudit) {
                        const notificationId = (0, uuid_1.v4)();
                        await connection.query(`INSERT INTO notifications (
            id, user_id, message, is_read, timestamp, voucher_id, type
          ) VALUES (?, ?, ?, ?, NOW(), ?, ?)`, [
                            notificationId,
                            voucher.department,
                            `Voucher ${voucher.voucher_id} certified by Audit`,
                            false,
                            voucherId,
                            'VOUCHER_CERTIFIED'
                        ]);
                    }
                    else {
                        const notificationId = (0, uuid_1.v4)();
                        await connection.query(`INSERT INTO notifications (
            id, user_id, message, is_read, timestamp, voucher_id, type
          ) VALUES (?, ?, ?, ?, NOW(), ?, ?)`, [
                            notificationId,
                            voucher.department,
                            `Voucher ${voucher.voucher_id} received from Audit`,
                            false,
                            voucherId,
                            'VOUCHER_RECEIVED'
                        ]);
                    }
                }
                for (const voucherId of rejectedVoucherIds) {
                    if (!allVoucherIds.includes(voucherId)) {
                        continue;
                    }
                    const comment = rejectionComments[voucherId] || '';
                    console.log(`🚫 BATCH REJECTION: Rejecting voucher ${voucherId} with simple workflow`);
                    const [voucherDetails] = await connection.query('SELECT * FROM vouchers WHERE id = ?', [voucherId]);
                    if (voucherDetails.length === 0) {
                        logger_js_1.logger.error(`Voucher ${voucherId} not found during batch rejection`);
                        continue;
                    }
                    const voucherToReject = voucherDetails[0];
                    const { flags: rejectedFlags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({ status: legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED });
                    await connection.query(`
        UPDATE vouchers SET
          status = ?,
          flags = ?,
          rejected_by = ?,
          rejection_time = NOW(),
          comment = ?,
          work_started = 1,
          department = 'AUDIT',
          rejection_type = 'DISPATCHABLE',
          workflow_state = 'AUDIT_PENDING_DISPATCH_REJECTED'
        WHERE id = ?
      `, [
                        legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED,
                        JSON.stringify(rejectedFlags),
                        req.user.name,
                        comment,
                        voucherId
                    ]);
                    console.log(`Creating copy for Audit REJECTED tab (permanent record)`);
                    const copyId = (0, uuid_1.v4)();
                    const { flags: copyFlags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({
                        status: legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED
                    });
                    await connection.query(`
        INSERT INTO vouchers (
          id, voucher_id, date, claimant, description, amount, currency,
          department, original_department, status, flags,
          created_by, created_at, sent_to_audit, received_by_audit, work_started,
          parent_voucher_id, is_rejection_copy,
          rejected_by, rejection_time, comment, rejection_type, workflow_state
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
                        copyId,
                        voucherToReject.voucher_id + '-COPY',
                        voucherToReject.date,
                        voucherToReject.claimant,
                        voucherToReject.description,
                        voucherToReject.amount,
                        voucherToReject.currency,
                        'AUDIT',
                        voucherToReject.original_department,
                        legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED,
                        JSON.stringify(copyFlags),
                        voucherToReject.created_by,
                        voucherToReject.created_at,
                        true,
                        true,
                        false,
                        voucherId,
                        true,
                        req.user.name,
                        new Date().toISOString().slice(0, 19).replace('T', ' '),
                        comment,
                        'PERMANENT_RECORD',
                        'AUDIT_REJECTED_COPY'
                    ]);
                    console.log(`✅ BATCH REJECTION: Simple rejection completed for ${voucherToReject.voucher_id}:`);
                    console.log(`   - Original voucher: ${voucherId} (goes to Audit PENDING DISPATCH tab)`);
                    console.log(`   - Copy: ${copyId} (goes to Audit REJECTED tab as permanent record)`);
                    const voucher = batchVouchers.find((v) => v.id === voucherId);
                    if (voucher) {
                        const notificationId = (0, uuid_1.v4)();
                        await connection.query(`INSERT INTO notifications (
            id, user_id, message, is_read, timestamp, voucher_id, type, from_audit
          ) VALUES (?, ?, ?, ?, NOW(), ?, ?, ?)`, [
                            notificationId,
                            isFromAudit ? 'AUDIT' : voucher.department,
                            `Voucher ${voucher.voucher_id} rejected`,
                            false,
                            voucherId,
                            'VOUCHER_REJECTED',
                            !isFromAudit
                        ]);
                    }
                }
                await connection.query('UPDATE voucher_batches SET received = TRUE WHERE id = ?', [batchId]);
                const returnBatch = batches[0];
                const allProcessedVoucherIds = [...receivedVoucherIds];
                if (!isFromAudit && allProcessedVoucherIds.length > 0) {
                    const existingReturnBatches = await connection.query(`SELECT DISTINCT vb.id FROM voucher_batches vb
         JOIN batch_vouchers bv ON vb.id = bv.batch_id
         WHERE vb.department = ? AND vb.from_audit = TRUE
         AND vb.sent_time > DATE_SUB(NOW(), INTERVAL 2 MINUTE)
         AND bv.voucher_id IN (${allProcessedVoucherIds.map(() => '?').join(',')})`, [returnBatch.department, ...allProcessedVoucherIds]);
                    if (existingReturnBatches.length > 0) {
                        logger_js_1.logger.warn(`⚠️ Duplicate return batch prevented - existing batch found for department ${returnBatch.department}`);
                    }
                    else {
                        const returnBatchId = (0, uuid_1.v4)();
                        await connection.query(`INSERT INTO voucher_batches (
            id, department, sent_by, sent_time, received, from_audit
          ) VALUES (?, ?, ?, NOW(), FALSE, TRUE)`, [
                            returnBatchId,
                            returnBatch.department,
                            req.user.name
                        ]);
                        for (const voucherId of allProcessedVoucherIds) {
                            await connection.query('INSERT INTO batch_vouchers (batch_id, voucher_id) VALUES (?, ?)', [returnBatchId, voucherId]);
                        }
                        logger_js_1.logger.info(`✅ Created return batch ${returnBatchId} for ${allProcessedVoucherIds.length} processed vouchers (${receivedVoucherIds.length} accepted, ${rejectedVoucherIds.length} rejected) to ${returnBatch.department}`);
                        if (returnBatch.department && returnBatch.department !== 'AUDIT') {
                            (0, socketHandlers_js_1.broadcastToDepartment)(returnBatch.department, 'new_batch_notification', {
                                batchId: returnBatchId,
                                department: 'AUDIT',
                                voucherCount: allProcessedVoucherIds.length,
                                sentBy: req.user.name,
                                timestamp: Date.now(),
                                message: `Batch processed: ${receivedVoucherIds.length} accepted, ${rejectedVoucherIds.length} rejected`
                            });
                            logger_js_1.logger.info(`📢 REAL-TIME: Notified ${returnBatch.department} about batch processing results`);
                        }
                    }
                }
                await connection.commit();
                const updatedBatches = await (0, db_js_1.query)('SELECT * FROM voucher_batches WHERE id = ?', [batchId]);
                const updatedBatchVouchers = await (0, db_js_1.query)(`SELECT v.* FROM vouchers v
       JOIN batch_vouchers bv ON v.id = bv.voucher_id
       WHERE bv.batch_id = ?`, [batchId]);
                const result = updatedBatches[0];
                result.vouchers = updatedBatchVouchers;
                result.voucherIds = updatedBatchVouchers.map((v) => v.id);
                logger_js_1.logger.info(`🔄 BATCH RECEIVE: Transaction completed successfully in ${Date.now() - startTime}ms`);
                return result;
            }),
            new Promise((_, reject) => {
                setTimeout(() => {
                    logger_js_1.logger.error(`❌ BATCH RECEIVE: Timeout after 15 seconds for batch ${batchId}`);
                    reject(new Error('Batch processing timeout after 15 seconds'));
                }, 15000);
            })
        ]);
        logger_js_1.logger.info(`✅ BATCH RECEIVE: Successfully completed batch ${batchId} in ${Date.now() - startTime}ms`);
        res.status(200).json(result);
    }
    catch (error) {
        const duration = Date.now() - startTime;
        logger_js_1.logger.error(`❌ BATCH RECEIVE: Error after ${duration}ms for batch ${batchId}:`, error);
        if (error instanceof Error) {
            if (error.message === 'Batch not found') {
                return res.status(404).json({ error: error.message });
            }
            if (error.message === 'Access denied') {
                return res.status(403).json({ error: error.message });
            }
            if (error.message.includes('timeout')) {
                return res.status(408).json({ error: 'Batch processing timeout - please try again' });
            }
        }
        res.status(500).json({ error: 'Failed to receive batch' });
    }
});

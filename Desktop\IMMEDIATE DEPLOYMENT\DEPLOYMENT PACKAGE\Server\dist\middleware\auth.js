"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticate = authenticate;
exports.authorize = authorize;
exports.attachDatabase = attachDatabase;
const logger_js_1 = require("../utils/logger.js");
async function authenticate(req, res, next) {
    try {
        logger_js_1.logger.info(`🔐 LAN Authentication for: ${req.method} ${req.url}`);
        const sessionId = req.headers['x-session-id'] || req.cookies?.vms_session_id || req.cookies?.sessionId;
        if (!sessionId) {
            logger_js_1.logger.warn('🔐 No session ID found - LAN mode requires login');
            return res.status(401).json({ error: 'Authentication required' });
        }
        try {
            const { query } = await Promise.resolve().then(() => __importStar(require('../database/db.js')));
            const sessions = await query('SELECT * FROM active_sessions WHERE id = ? AND is_active = TRUE', [sessionId]);
            if (sessions.length === 0) {
                logger_js_1.logger.warn(`Invalid session ID: ${sessionId}`);
                return res.status(401).json({ error: 'Invalid session' });
            }
            const session = sessions[0];
            const sessionAge = Date.now() - new Date(session.session_start).getTime();
            const maxAge = 8 * 60 * 60 * 1000;
            if (sessionAge > maxAge) {
                await query('UPDATE active_sessions SET is_active = FALSE, session_end = NOW() WHERE id = ?', [sessionId]);
                logger_js_1.logger.warn(`Session expired after 8 hours: ${sessionId}`);
                return res.status(401).json({ error: 'Session expired after 8 hours' });
            }
            const inactivityAge = Date.now() - new Date(session.last_activity).getTime();
            const inactivityTimeout = 30 * 60 * 1000;
            if (inactivityAge > inactivityTimeout) {
                await query('UPDATE active_sessions SET is_active = FALSE, session_end = NOW() WHERE id = ?', [sessionId]);
                logger_js_1.logger.warn(`Session expired due to 30-minute inactivity: ${sessionId}`);
                return res.status(401).json({ error: 'Session expired due to inactivity (30 minutes)' });
            }
            await query('UPDATE active_sessions SET last_activity = NOW() WHERE id = ?', [sessionId]);
            const users = await query('SELECT * FROM users WHERE id = ?', [session.user_id]);
            if (users.length === 0) {
                logger_js_1.logger.warn(`User not found for session: ${sessionId}`);
                return res.status(401).json({ error: 'User not found' });
            }
            const user = users[0];
            req.user = {
                id: user.id,
                name: user.name,
                department: user.department,
                role: user.role,
                sessionId: sessionId
            };
            logger_js_1.logger.debug(`LAN Authentication successful for user: ${user.name}`);
            next();
        }
        catch (dbError) {
            logger_js_1.logger.error('Session validation error:', dbError);
            return res.status(500).json({ error: 'Authentication error' });
        }
    }
    catch (error) {
        logger_js_1.logger.error('Authentication middleware error:', error);
        return res.status(500).json({ error: 'Authentication error' });
    }
}
function authorize(roles) {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        if (!roles.includes(req.user.role)) {
            return res.status(403).json({ error: 'Insufficient permissions' });
        }
        next();
    };
}
async function attachDatabase(req, res, next) {
    try {
        const { query, getTransaction } = await Promise.resolve().then(() => __importStar(require('../database/db.js')));
        req.db = {
            execute: query,
            query: query,
            getTransaction: getTransaction
        };
        next();
    }
    catch (error) {
        logger_js_1.logger.error('Database middleware error:', error);
        res.status(500).json({ error: 'Database connection error' });
    }
}

"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.auditRouter = void 0;
const express_1 = __importDefault(require("express"));
const uuid_1 = require("uuid");
const db_js_1 = require("../database/db.js");
const auth_js_1 = require("../middleware/auth.js");
const logger_js_1 = require("../utils/logger.js");
exports.auditRouter = express_1.default.Router();
exports.auditRouter.use(auth_js_1.authenticate);
function hasAuditAccess(req, res, next) {
    if (req.user.department === 'AUDIT' || req.user.department === 'SYSTEM ADMIN') {
        next();
    }
    else {
        res.status(403).json({ error: 'Access denied' });
    }
}
exports.auditRouter.use(hasAuditAccess);
exports.auditRouter.get('/dashboard', async (req, res) => {
    try {
        const pendingCount = await (0, db_js_1.query)("SELECT COUNT(*) as count FROM vouchers WHERE status = 'PENDING RECEIPT' OR status = 'AUDIT: PROCESSING'");
        const certifiedCount = await (0, db_js_1.query)("SELECT COUNT(*) as count FROM vouchers WHERE status = 'VOUCHER CERTIFIED'");
        const rejectedCount = await (0, db_js_1.query)("SELECT COUNT(*) as count FROM vouchers WHERE status = 'VOUCHER REJECTED'");
        const returnedCount = await (0, db_js_1.query)("SELECT COUNT(*) as count FROM vouchers WHERE status = 'VOUCHER RETURNED'");
        const departmentCounts = await (0, db_js_1.query)("SELECT department, COUNT(*) as count FROM vouchers GROUP BY department");
        const recentVouchers = await (0, db_js_1.query)("SELECT * FROM vouchers ORDER BY date DESC LIMIT 10");
        const provisionalCashRecords = await (0, db_js_1.query)("SELECT * FROM provisional_cash_records ORDER BY date DESC LIMIT 10");
        res.json({
            counts: {
                pending: pendingCount[0].count,
                certified: certifiedCount[0].count,
                rejected: rejectedCount[0].count,
                returned: returnedCount[0].count
            },
            departmentCounts,
            recentVouchers,
            provisionalCashRecords
        });
    }
    catch (error) {
        logger_js_1.logger.error('Get audit dashboard data error:', error);
        res.status(500).json({ error: 'Failed to get audit dashboard data' });
    }
});
exports.auditRouter.get('/analytics', async (req, res) => {
    try {
        const { startDate, endDate } = req.query;
        let dateFilter = '';
        const params = [];
        if (startDate && endDate) {
            dateFilter = 'WHERE date BETWEEN ? AND ?';
            params.push(startDate, endDate);
        }
        else if (startDate) {
            dateFilter = 'WHERE date >= ?';
            params.push(startDate);
        }
        else if (endDate) {
            dateFilter = 'WHERE date <= ?';
            params.push(endDate);
        }
        const statusCounts = await (0, db_js_1.query)(`SELECT status, COUNT(*) as count FROM vouchers ${dateFilter} GROUP BY status`, params);
        const departmentCounts = await (0, db_js_1.query)(`SELECT department, COUNT(*) as count FROM vouchers ${dateFilter} GROUP BY department`, params);
        const departmentAmounts = await (0, db_js_1.query)(`SELECT department, SUM(amount) as total FROM vouchers ${dateFilter} GROUP BY department`, params);
        const monthCounts = await (0, db_js_1.query)(`SELECT SUBSTRING(date, 1, 3) as month, COUNT(*) as count 
       FROM vouchers ${dateFilter} 
       GROUP BY SUBSTRING(date, 1, 3)
       ORDER BY FIELD(SUBSTRING(date, 1, 3), 'JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC')`, params);
        res.json({
            statusCounts,
            departmentCounts,
            departmentAmounts,
            monthCounts
        });
    }
    catch (error) {
        logger_js_1.logger.error('Get audit analytics data error:', error);
        res.status(500).json({ error: 'Failed to get audit analytics data' });
    }
});
exports.auditRouter.post('/log', async (req, res) => {
    try {
        const { action, resourceType, resourceId, details } = req.body;
        if (!action || !resourceType) {
            return res.status(400).json({ error: 'Action and resource type are required' });
        }
        const id = (0, uuid_1.v4)();
        await (0, db_js_1.query)(`INSERT INTO audit_logs (
        id, user_id, action, resource_type, resource_id, details, timestamp, ip_address
      ) VALUES (?, ?, ?, ?, ?, ?, NOW(), ?)`, [
            id,
            req.user.id,
            action,
            resourceType,
            resourceId || null,
            details || null,
            req.ip
        ]);
        const logs = await (0, db_js_1.query)('SELECT * FROM audit_logs WHERE id = ?', [id]);
        res.status(201).json(logs[0]);
    }
    catch (error) {
        logger_js_1.logger.error('Create audit log error:', error);
        res.status(500).json({ error: 'Failed to create audit log' });
    }
});
exports.auditRouter.get('/logs/:resourceType/:resourceId', async (req, res) => {
    try {
        const { resourceType, resourceId } = req.params;
        const logs = await (0, db_js_1.query)('SELECT * FROM audit_logs WHERE resource_type = ? AND resource_id = ? ORDER BY timestamp DESC', [resourceType, resourceId]);
        res.json(logs);
    }
    catch (error) {
        logger_js_1.logger.error('Get resource audit logs error:', error);
        res.status(500).json({ error: 'Failed to get resource audit logs' });
    }
});

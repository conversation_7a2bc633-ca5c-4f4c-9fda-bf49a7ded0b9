"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.eventBus = void 0;
const events_1 = require("events");
class VMSEventBus extends events_1.EventEmitter {
    static instance;
    constructor() {
        super();
        this.setMaxListeners(50);
        console.log('🎯 VMS Event Bus initialized');
    }
    static getInstance() {
        if (!VMSEventBus.instance) {
            VMSEventBus.instance = new VMSEventBus();
        }
        return VMSEventBus.instance;
    }
    emitBatchCreated(batchData) {
        console.log(`📡 Event: Batch created - ${batchData.id}`);
        this.emit('batch:created', batchData);
    }
    emitBatchUpdated(batchData) {
        console.log(`📡 Event: Batch updated - ${batchData.id}`);
        this.emit('batch:updated', batchData);
    }
    emitBatchReceived(batchData) {
        console.log(`📡 Event: Batch received - ${batchData.id}`);
        this.emit('batch:received', batchData);
    }
    emitNotificationCreated(notificationData) {
        console.log(`📡 Event: Notification created for ${notificationData.user_id}`);
        this.emit('notification:created', notificationData);
    }
    emitVoucherUpdated(voucherData) {
        console.log(`📡 Event: Voucher updated - ${voucherData.id}`);
        this.emit('voucher:updated', voucherData);
    }
    emitDataUpdate(entityType, actionType, data) {
        console.log(`📡 Event: ${entityType} ${actionType} - ${data.id || 'unknown'}`);
        this.emit('data:update', { entityType, actionType, data });
    }
}
exports.eventBus = VMSEventBus.getInstance();

NODE_ENV=development
PORT=3000
HOST=0.0.0.0
VMS_ROOT=C:\Users\<USER>\Desktop\IMMEDIATE DEPLOYMENT\DEPLOYMENT PACKAGE
VMS_SERVER_ROOT=C:\Users\<USER>\Desktop\IMMEDIATE DEPLOYMENT\DEPLOYMENT PACKAGE\Server
VMS_LOGS_ROOT=C:\Users\<USER>\Desktop\IMMEDIATE DEPLOYMENT\DEPLOYMENT PACKAGE\Server\logs
VMS_CONFIG_ROOT=C:\Users\<USER>\Desktop\IMMEDIATE DEPLOYMENT\DEPLOYMENT PACKAGE\Config
VMS_BACKUP_ROOT=C:\Users\<USER>\Desktop\IMMEDIATE DEPLOYMENT\DEPLOYMENT PACKAGE\Backups
VMS_DEPLOYMENT_ID=vms-dev-testing-$(date +%s)
VMS_DEPLOYMENT_MODE=development
VMS_SERVER_IP=127.0.0.1
VMS_PORTABLE_MODE=true

# Database Configuration (SQLite for development testing)
DB_TYPE=sqlite
DB_PATH=C:\Users\<USER>\Desktop\IMMEDIATE DEPLOYMENT\DEPLOYMENT PACKAGE\Server\vms_dev.db

# MySQL Configuration (commented out for development)
# DB_HOST=localhost
# DB_PORT=3306
# DB_NAME=vms_production
# DB_USER=vms_user
# DB_PASSWORD=your_password
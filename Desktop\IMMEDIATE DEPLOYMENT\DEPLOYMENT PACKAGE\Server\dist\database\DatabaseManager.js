"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseManager = exports.DatabaseManager = void 0;
const promise_1 = __importDefault(require("mysql2/promise"));
const logger_js_1 = require("../utils/logger.js");
class DatabaseManager {
    pool = null;
    isConnected = false;
    reconnectAttempts = 0;
    maxReconnectAttempts = 5;
    reconnectDelay = 5000;
    healthCheckInterval = null;
    connectionConfig;
    constructor() {
        this.connectionConfig = {
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT || '3306'),
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || 'vms@2025@1989',
            database: process.env.DB_NAME || 'vms_production',
            waitForConnections: true,
            connectionLimit: 50,
            queueLimit: 100,
            multipleStatements: false,
            charset: 'utf8mb4',
            timezone: '+00:00',
            supportBigNumbers: true,
            bigNumberStrings: true,
            enableKeepAlive: true,
            keepAliveInitialDelay: 0,
        };
        this.setupGracefulShutdown();
    }
    async initialize() {
        try {
            await this.createPool();
            await this.testConnection();
            await this.setupDatabase();
            this.startHealthCheck();
            this.isConnected = true;
            this.reconnectAttempts = 0;
            logger_js_1.logger.info('Database Manager initialized successfully');
        }
        catch (error) {
            logger_js_1.logger.error('Failed to initialize database:', error);
            await this.handleConnectionFailure();
        }
    }
    async createPool() {
        this.pool = promise_1.default.createPool(this.connectionConfig);
        this.pool.on('connection', (connection) => {
            logger_js_1.logger.info(`New database connection established: ${connection.threadId}`);
        });
    }
    async testConnection() {
        if (!this.pool)
            throw new Error('Pool not initialized');
        const connection = await this.pool.getConnection();
        try {
            await connection.ping();
            logger_js_1.logger.info('Database connection test successful');
        }
        finally {
            connection.release();
        }
    }
    async setupDatabase() {
        try {
            await this.createDatabaseIfNotExists();
            await this.verifyTables();
            logger_js_1.logger.info('Database setup verification complete');
        }
        catch (error) {
            logger_js_1.logger.error('Database setup failed:', error);
            throw error;
        }
    }
    async createDatabaseIfNotExists() {
        const tempConnection = await promise_1.default.createConnection({
            host: this.connectionConfig.host,
            port: this.connectionConfig.port,
            user: this.connectionConfig.user,
            password: this.connectionConfig.password,
        });
        try {
            await tempConnection.query(`CREATE DATABASE IF NOT EXISTS ${this.connectionConfig.database}`);
            logger_js_1.logger.info(`Database '${this.connectionConfig.database}' verified`);
        }
        finally {
            await tempConnection.end();
        }
    }
    async verifyTables() {
        const essentialTables = ['users', 'vouchers', 'voucher_batches'];
        for (const table of essentialTables) {
            const [rows] = await this.pool.query('SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = ?', [this.connectionConfig.database, table]);
            if (rows[0].count === 0) {
                throw new Error(`Essential table '${table}' does not exist`);
            }
        }
        logger_js_1.logger.info('Essential database tables verified');
    }
    async query(sql, params, retryCount = 0) {
        const maxRetries = 3;
        if (!this.isConnected || !this.pool) {
            if (retryCount === 0) {
                logger_js_1.logger.warn('⚠️ Database not connected - attempting to reconnect...');
                try {
                    await this.initialize();
                }
                catch (error) {
                    throw new Error('Database unavailable - connection failed');
                }
            }
            else {
                throw new Error('Database not connected');
            }
        }
        try {
            const startTime = Date.now();
            const [results] = await this.pool.execute(sql, params);
            const duration = Date.now() - startTime;
            if (duration > 5000) {
                logger_js_1.logger.warn(`🐌 Slow query detected (${duration}ms):`, {
                    sql: sql.substring(0, 100) + (sql.length > 100 ? '...' : ''),
                    duration
                });
            }
            return results;
        }
        catch (error) {
            logger_js_1.logger.error('❌ Database query error:', {
                sql: sql.substring(0, 100) + (sql.length > 100 ? '...' : ''),
                error: error.message,
                code: error.code,
                retryCount
            });
            if (this.isConnectionError(error) && retryCount < maxRetries) {
                logger_js_1.logger.info(`🔄 Retrying query due to connection error (attempt ${retryCount + 1}/${maxRetries})`);
                await this.handleConnectionFailure();
                const delay = Math.min(1000 * Math.pow(2, retryCount), 5000);
                await new Promise(resolve => setTimeout(resolve, delay));
                return this.query(sql, params, retryCount + 1);
            }
            if (error.code === 'ER_DUP_ENTRY') {
                throw new Error('Duplicate entry - this record already exists');
            }
            else if (error.code === 'ER_NO_REFERENCED_ROW_2') {
                throw new Error('Invalid reference - related record not found');
            }
            else if (error.code === 'ER_ROW_IS_REFERENCED_2') {
                throw new Error('Cannot delete - record is referenced by other data');
            }
            else if (this.isConnectionError(error)) {
                throw new Error('Database connection lost - please try again');
            }
            throw error;
        }
    }
    async withTransaction(callback, retryCount = 0) {
        const maxRetries = 2;
        if (!this.pool) {
            throw new Error('Database not connected');
        }
        let connection = null;
        try {
            connection = await Promise.race([
                this.pool.getConnection(),
                new Promise((_, reject) => setTimeout(() => reject(new Error('Connection timeout')), 30000))
            ]);
            await connection.beginTransaction();
            const startTime = Date.now();
            const result = await callback(connection);
            const duration = Date.now() - startTime;
            await connection.commit();
            if (duration > 10000) {
                logger_js_1.logger.warn(`🐌 Long transaction detected: ${duration}ms`);
            }
            return result;
        }
        catch (error) {
            if (connection) {
                try {
                    await connection.rollback();
                    logger_js_1.logger.info('✅ Transaction rolled back successfully');
                }
                catch (rollbackError) {
                    logger_js_1.logger.error('❌ Failed to rollback transaction:', rollbackError);
                }
            }
            logger_js_1.logger.error('❌ Transaction failed:', {
                error: error.message,
                code: error.code,
                retryCount
            });
            if (this.isConnectionError(error) && retryCount < maxRetries) {
                logger_js_1.logger.info(`🔄 Retrying transaction due to connection error (attempt ${retryCount + 1}/${maxRetries})`);
                await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
                return this.withTransaction(callback, retryCount + 1);
            }
            throw error;
        }
        finally {
            if (connection) {
                try {
                    connection.release();
                }
                catch (releaseError) {
                    logger_js_1.logger.error('❌ Failed to release connection:', releaseError);
                }
            }
        }
    }
    async getHealthStatus() {
        try {
            if (!this.pool)
                return { connected: false, poolStatus: null, uptime: 0 };
            const connection = await this.pool.getConnection();
            try {
                await connection.ping();
                return {
                    connected: true,
                    poolStatus: {
                        available: true,
                        healthy: true
                    },
                    uptime: process.uptime()
                };
            }
            finally {
                connection.release();
            }
        }
        catch (error) {
            return { connected: false, poolStatus: null, uptime: 0 };
        }
    }
    startHealthCheck() {
        logger_js_1.logger.info('Health checks DISABLED to prevent infinite loops');
    }
    async handleConnectionFailure() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            logger_js_1.logger.error('❌ Max reconnection attempts reached - entering degraded mode');
            this.isConnected = false;
            setTimeout(() => {
                logger_js_1.logger.info('🔄 Attempting final recovery after extended delay...');
                this.reconnectAttempts = 0;
                this.handleConnectionFailure();
            }, 300000);
            return;
        }
        this.isConnected = false;
        this.reconnectAttempts++;
        const baseDelay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        const jitter = Math.random() * 1000;
        const delay = Math.min(baseDelay + jitter, 60000);
        logger_js_1.logger.warn(`⚠️ Database connection lost - attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${Math.round(delay / 1000)}s`);
        setTimeout(async () => {
            try {
                if (this.pool) {
                    await this.pool.end();
                    this.pool = null;
                }
                await this.initialize();
                logger_js_1.logger.info('✅ Database connection restored successfully');
            }
            catch (error) {
                logger_js_1.logger.error(`❌ Reconnection attempt ${this.reconnectAttempts} failed:`, {
                    error: error.message,
                    code: error.code,
                    errno: error.errno
                });
                await this.handleConnectionFailure();
            }
        }, delay);
    }
    isConnectionError(error) {
        const connectionErrors = [
            'PROTOCOL_CONNECTION_LOST',
            'ECONNRESET',
            'ENOTFOUND',
            'ECONNREFUSED',
            'ETIMEDOUT'
        ];
        return connectionErrors.includes(error.code);
    }
    setupGracefulShutdown() {
        const shutdown = async () => {
            logger_js_1.logger.info('Shutting down database manager...');
            if (this.healthCheckInterval) {
                clearInterval(this.healthCheckInterval);
            }
            if (this.pool) {
                await this.pool.end();
                logger_js_1.logger.info('Database connections closed');
            }
        };
        process.on('SIGTERM', shutdown);
        process.on('SIGINT', shutdown);
    }
    getPoolStats() {
        if (!this.pool) {
            return { error: 'Pool not initialized' };
        }
        const pool = this.pool;
        return {
            totalConnections: pool._allConnections?.length || 0,
            freeConnections: pool._freeConnections?.length || 0,
            acquiringConnections: pool._acquiringConnections?.length || 0,
            queuedRequests: pool._connectionQueue?.length || 0,
            connectionLimit: this.connectionConfig.connectionLimit || 50,
            queueLimit: this.connectionConfig.queueLimit || 100
        };
    }
    logPoolHealth() {
        const stats = this.getPoolStats();
        if ('error' in stats) {
            logger_js_1.logger.warn('📊 Pool stats unavailable:', stats.error);
            return;
        }
        const utilizationPercent = Math.round((stats.totalConnections / stats.connectionLimit) * 100);
        const queueUtilizationPercent = Math.round((stats.queuedRequests / stats.queueLimit) * 100);
        logger_js_1.logger.info('📊 DB Pool Health:', {
            connections: `${stats.totalConnections}/${stats.connectionLimit} (${utilizationPercent}%)`,
            free: stats.freeConnections,
            acquiring: stats.acquiringConnections,
            queued: `${stats.queuedRequests}/${stats.queueLimit} (${queueUtilizationPercent}%)`
        });
        if (utilizationPercent > 80) {
            logger_js_1.logger.warn(`⚠️ High DB connection utilization: ${utilizationPercent}%`);
        }
        if (queueUtilizationPercent > 50) {
            logger_js_1.logger.warn(`⚠️ High DB queue utilization: ${queueUtilizationPercent}%`);
        }
    }
    isHealthy() {
        return this.isConnected && this.pool !== null;
    }
}
exports.DatabaseManager = DatabaseManager;
exports.databaseManager = new DatabaseManager();

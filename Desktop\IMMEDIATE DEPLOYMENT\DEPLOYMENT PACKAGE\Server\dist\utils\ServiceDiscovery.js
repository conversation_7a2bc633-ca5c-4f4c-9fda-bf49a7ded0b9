"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.serviceDiscovery = exports.ServiceDiscovery = void 0;
const dgram_1 = __importDefault(require("dgram"));
const os_1 = __importDefault(require("os"));
const logger_js_1 = require("./logger.js");
class ServiceDiscovery {
    static instance;
    broadcastSocket = null;
    discoverySocket = null;
    serviceInfo = null;
    broadcastInterval = null;
    isRunning = false;
    BROADCAST_PORT = 45454;
    DISCOVERY_PORT = 45455;
    BROADCAST_INTERVAL = 5000;
    SERVICE_NAME = 'VMS-Server';
    constructor() { }
    static getInstance() {
        if (!ServiceDiscovery.instance) {
            ServiceDiscovery.instance = new ServiceDiscovery();
        }
        return ServiceDiscovery.instance;
    }
    async startAnnouncement(port) {
        if (this.isRunning) {
            logger_js_1.logger.warn('🔊 Service discovery already running');
            return;
        }
        try {
            const localIPs = this.getLocalIPAddresses();
            this.serviceInfo = {
                serviceName: this.SERVICE_NAME,
                host: localIPs[0] || 'localhost',
                port: port,
                version: '3.0.0',
                timestamp: Date.now(),
                capabilities: ['voucher-management', 'real-time-updates', 'multi-user']
            };
            this.broadcastSocket = dgram_1.default.createSocket('udp4');
            this.broadcastSocket.bind(() => {
                this.broadcastSocket.setBroadcast(true);
                logger_js_1.logger.info(`📡 Service discovery broadcast started on port ${this.BROADCAST_PORT}`);
            });
            this.discoverySocket = dgram_1.default.createSocket('udp4');
            this.discoverySocket.bind(this.DISCOVERY_PORT, () => {
                logger_js_1.logger.info(`🔍 Service discovery listener started on port ${this.DISCOVERY_PORT}`);
            });
            this.discoverySocket.on('message', (msg, rinfo) => {
                try {
                    const request = JSON.parse(msg.toString());
                    if (request.type === 'VMS_DISCOVERY_REQUEST') {
                        this.sendDiscoveryResponse(rinfo.address, rinfo.port);
                    }
                }
                catch (error) {
                }
            });
            this.startPeriodicAnnouncements();
            this.isRunning = true;
            logger_js_1.logger.info(`🎯 VMS Service Discovery started - Broadcasting on all network interfaces`);
            logger_js_1.logger.info(`📊 Service Info: ${JSON.stringify(this.serviceInfo, null, 2)}`);
        }
        catch (error) {
            logger_js_1.logger.error('❌ Failed to start service discovery:', error);
            throw error;
        }
    }
    async stopAnnouncement() {
        if (!this.isRunning)
            return;
        try {
            if (this.broadcastInterval) {
                clearInterval(this.broadcastInterval);
                this.broadcastInterval = null;
            }
            if (this.broadcastSocket) {
                this.broadcastSocket.close();
                this.broadcastSocket = null;
            }
            if (this.discoverySocket) {
                this.discoverySocket.close();
                this.discoverySocket = null;
            }
            this.isRunning = false;
            logger_js_1.logger.info('🔇 Service discovery stopped');
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error stopping service discovery:', error);
        }
    }
    sendDiscoveryResponse(clientIP, clientPort) {
        if (!this.serviceInfo || !this.broadcastSocket)
            return;
        const response = {
            type: 'VMS_DISCOVERY_RESPONSE',
            service: this.serviceInfo,
            localIPs: this.getLocalIPAddresses()
        };
        const message = Buffer.from(JSON.stringify(response));
        this.broadcastSocket.send(message, clientPort, clientIP, (error) => {
            if (error) {
                logger_js_1.logger.error(`❌ Failed to send discovery response to ${clientIP}:${clientPort}:`, error);
            }
            else {
                logger_js_1.logger.info(`📤 Sent discovery response to ${clientIP}:${clientPort}`);
            }
        });
    }
    startPeriodicAnnouncements() {
        this.broadcastInterval = setInterval(() => {
            this.broadcastServiceInfo();
        }, this.BROADCAST_INTERVAL);
        this.broadcastServiceInfo();
    }
    broadcastServiceInfo() {
        if (!this.serviceInfo || !this.broadcastSocket)
            return;
        const announcement = {
            type: 'VMS_SERVICE_ANNOUNCEMENT',
            service: {
                ...this.serviceInfo,
                timestamp: Date.now()
            },
            localIPs: this.getLocalIPAddresses()
        };
        const message = Buffer.from(JSON.stringify(announcement));
        const broadcastAddresses = this.getBroadcastAddresses();
        broadcastAddresses.forEach(address => {
            this.broadcastSocket.send(message, this.BROADCAST_PORT, address, (error) => {
                if (error) {
                    logger_js_1.logger.debug(`❌ Broadcast failed to ${address}:`, error.message);
                }
                else {
                    logger_js_1.logger.debug(`📡 Broadcasted to ${address}:${this.BROADCAST_PORT}`);
                }
            });
        });
    }
    getLocalIPAddresses() {
        const interfaces = os_1.default.networkInterfaces();
        const addresses = [];
        for (const name of Object.keys(interfaces)) {
            const nets = interfaces[name];
            if (!nets)
                continue;
            for (const net of nets) {
                if (net.family === 'IPv4' && !net.internal) {
                    addresses.push(net.address);
                }
            }
        }
        return addresses;
    }
    getBroadcastAddresses() {
        const interfaces = os_1.default.networkInterfaces();
        const broadcasts = ['***************'];
        for (const name of Object.keys(interfaces)) {
            const nets = interfaces[name];
            if (!nets)
                continue;
            for (const net of nets) {
                if (net.family === 'IPv4' && !net.internal && net.netmask) {
                    const broadcast = this.calculateBroadcastAddress(net.address, net.netmask);
                    if (broadcast && !broadcasts.includes(broadcast)) {
                        broadcasts.push(broadcast);
                    }
                }
            }
        }
        return broadcasts;
    }
    calculateBroadcastAddress(ip, netmask) {
        try {
            const ipParts = ip.split('.').map(Number);
            const maskParts = netmask.split('.').map(Number);
            const broadcast = ipParts.map((part, index) => {
                return part | (255 - maskParts[index]);
            });
            return broadcast.join('.');
        }
        catch (error) {
            return null;
        }
    }
    getServiceInfo() {
        return this.serviceInfo;
    }
    isServiceRunning() {
        return this.isRunning;
    }
    updateServicePort(newPort) {
        if (this.serviceInfo) {
            this.serviceInfo.port = newPort;
            this.serviceInfo.timestamp = Date.now();
            logger_js_1.logger.info(`🔄 Service port updated to: ${newPort}`);
        }
    }
}
exports.ServiceDiscovery = ServiceDiscovery;
exports.serviceDiscovery = ServiceDiscovery.getInstance();

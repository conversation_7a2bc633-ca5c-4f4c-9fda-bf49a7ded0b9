"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const VoucherWorkflowStateMachine_1 = require("../workflow/VoucherWorkflowStateMachine");
const auth_1 = require("../middleware/auth");
const workflow_validation_1 = require("../middleware/workflow-validation");
const workflow_service_1 = require("../services/workflow-service");
const router = express_1.default.Router();
router.use(auth_1.attachDatabase);
router.get('/vouchers/:department', auth_1.authenticate, async (req, res) => {
    try {
        const { department } = req.params;
        const { tab, search, dateRange } = req.query;
        const query = `
      SELECT v.*, 
             CASE 
               WHEN v.workflow_state LIKE 'AUDIT_%' AND ? = 'AUDIT' THEN 1
               WHEN v.workflow_state LIKE 'FINANCE_%' AND v.original_department = ? THEN 1
               ELSE 0
             END as is_visible
      FROM vouchers v
      WHERE v.deleted = FALSE
      HAVING is_visible = 1
      ORDER BY 
        CASE v.badge_type
          WHEN 'RE_SUBMITTED' THEN 4

          WHEN 'REJECTED' THEN 2
          ELSE 1
        END DESC,
        v.created_at DESC
    `;
        const [vouchersResult] = await req.db.execute(query, [department, department]);
        const vouchers = Array.isArray(vouchersResult) ? vouchersResult : [];
        let filteredVouchers = vouchers;
        if (tab) {
            filteredVouchers = vouchers.filter((voucher) => {
                const voucherTab = VoucherWorkflowStateMachine_1.VoucherWorkflowStateMachine.getTabForVoucher(voucher, department);
                return voucherTab === tab;
            });
        }
        if (search) {
            const searchTerm = search.toString().toLowerCase();
            filteredVouchers = filteredVouchers.filter((voucher) => voucher.voucher_id.toLowerCase().includes(searchTerm) ||
                voucher.description?.toLowerCase().includes(searchTerm) ||
                voucher.payee_name?.toLowerCase().includes(searchTerm));
        }
        const tabCounts = {};
        const availableTabs = VoucherWorkflowStateMachine_1.VoucherWorkflowStateMachine.getAvailableTabs(department);
        availableTabs.forEach(tabName => {
            tabCounts[tabName] = vouchers.filter((voucher) => {
                const voucherTab = VoucherWorkflowStateMachine_1.VoucherWorkflowStateMachine.getTabForVoucher(voucher, department);
                return voucherTab === tabName;
            }).length;
        });
        res.json({
            success: true,
            data: {
                vouchers: filteredVouchers,
                tabCounts,
                totalCount: vouchers.length,
                filteredCount: filteredVouchers.length
            }
        });
    }
    catch (error) {
        console.error('Error fetching workflow vouchers:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch vouchers'
        });
    }
});
router.post('/transition', auth_1.authenticate, workflow_validation_1.validateWorkflowTransition, async (req, res) => {
    try {
        const { voucherId, event, metadata } = req.body;
        const userId = req.user.id;
        const userDepartment = req.user.department;
        const workflowManager = (0, workflow_service_1.getWorkflowManager)();
        const result = await workflowManager.transitionVoucher({
            type: event,
            voucherId,
            context: {
                voucherId,
                userId,
                userDepartment,
                originalDepartment: req.body.originalDepartment,
                timestamp: new Date(),
                metadata
            }
        });
        if (result.success && result.voucher) {
            const { broadcastVoucherUpdate } = await Promise.resolve().then(() => __importStar(require('../socket/socketHandlers.js')));
            broadcastVoucherUpdate('workflow_transition', {
                id: result.voucher.id,
                voucher_id: result.voucher.voucher_id,
                status: result.voucher.status,
                department: result.voucher.department,
                original_department: result.voucher.original_department,
                workflow_state: result.voucher.workflow_state,
                badge_type: result.voucher.badge_type,
                previousState: result.previousState,
                newState: result.newState
            });
            console.log(`📢 REAL-TIME: Broadcasted workflow transition for ${result.voucher.voucher_id}: ${result.previousState} → ${result.newState}`);
        }
        res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        console.error('Error executing workflow transition:', error);
        res.status(400).json({
            success: false,
            error: error instanceof Error ? error.message : 'Failed to execute workflow transition'
        });
    }
});
router.get('/voucher/:id/actions', auth_1.authenticate, async (req, res) => {
    try {
        const { id } = req.params;
        const userDepartment = req.user.department;
        const [vouchers] = await req.db.execute('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [id]);
        if (vouchers.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'Voucher not found'
            });
        }
        const voucher = vouchers[0];
        const currentState = voucher.workflow_state;
        const validEvents = VoucherWorkflowStateMachine_1.VoucherWorkflowStateMachine.getValidEvents(currentState);
        const availableEvents = validEvents.filter(event => {
            if (userDepartment === 'AUDIT') {
                return [
                    VoucherWorkflowStateMachine_1.WorkflowEvent.RECEIVE_FROM_FINANCE,
                    VoucherWorkflowStateMachine_1.WorkflowEvent.START_WORK,
                    VoucherWorkflowStateMachine_1.WorkflowEvent.CERTIFY_VOUCHER,
                    VoucherWorkflowStateMachine_1.WorkflowEvent.REJECT_VOUCHER,
                    VoucherWorkflowStateMachine_1.WorkflowEvent.RETURN_VOUCHER,
                    VoucherWorkflowStateMachine_1.WorkflowEvent.DISPATCH_TO_FINANCE
                ].includes(event);
            }
            else {
                return [
                    VoucherWorkflowStateMachine_1.WorkflowEvent.SUBMIT_TO_AUDIT,
                    VoucherWorkflowStateMachine_1.WorkflowEvent.RESUBMIT_FROM_REJECTED,
                    VoucherWorkflowStateMachine_1.WorkflowEvent.RESUBMIT_FROM_RETURNED,
                ].includes(event);
            }
        });
        const actions = availableEvents.map(event => ({
            event,
            name: getActionName(event),
            requiresConfirmation: requiresConfirmation(event),
            icon: getActionIcon(event)
        }));
        res.json({
            success: true,
            data: {
                voucher: {
                    id: voucher.id,
                    voucher_id: voucher.voucher_id,
                    workflow_state: voucher.workflow_state,
                    badge_type: voucher.badge_type
                },
                actions,
                currentState,
                isEditable: VoucherWorkflowStateMachine_1.VoucherWorkflowStateMachine.isVoucherEditable(voucher, userDepartment)
            }
        });
    }
    catch (error) {
        console.error('Error getting voucher actions:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get voucher actions'
        });
    }
});
router.get('/audit-log/:voucherId', auth_1.authenticate, async (req, res) => {
    try {
        const { voucherId } = req.params;
        const [logs] = await req.db.execute(`
      SELECT 
        wal.*,
        u.name as user_name,
        u.department as user_department
      FROM workflow_audit_log wal
      LEFT JOIN users u ON wal.user_id = u.id
      WHERE wal.voucher_id = ?
      ORDER BY wal.timestamp DESC
    `, [voucherId]);
        res.json({
            success: true,
            data: logs
        });
    }
    catch (error) {
        console.error('Error fetching audit log:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch audit log'
        });
    }
});
router.get('/stats/:department', auth_1.authenticate, async (req, res) => {
    try {
        const { department } = req.params;
        const { period = '30' } = req.query;
        const query = `
      SELECT 
        v.workflow_state,
        v.badge_type,
        COUNT(*) as count,
        AVG(TIMESTAMPDIFF(HOUR, v.created_at, COALESCE(v.last_modified, NOW()))) as avg_processing_hours
      FROM vouchers v
      WHERE v.deleted = FALSE
        AND v.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        AND (
          (? = 'AUDIT' AND v.workflow_state LIKE 'AUDIT_%') OR
          (? != 'AUDIT' AND v.workflow_state LIKE 'FINANCE_%' AND v.original_department = ?)
        )
      GROUP BY v.workflow_state, v.badge_type
      ORDER BY count DESC
    `;
        const [stats] = await req.db.execute(query, [period, department, department, department]);
        res.json({
            success: true,
            data: {
                period: `${period} days`,
                statistics: stats
            }
        });
    }
    catch (error) {
        console.error('Error fetching workflow stats:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch workflow statistics'
        });
    }
});
function getActionName(event) {
    const actionNames = {
        [VoucherWorkflowStateMachine_1.WorkflowEvent.CREATE_VOUCHER]: 'Create Voucher',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.SUBMIT_TO_AUDIT]: 'Submit to Audit',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.RECEIVE_FROM_FINANCE]: 'Receive from Finance',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.START_WORK]: 'Start Work',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.CERTIFY_VOUCHER]: 'Certify Voucher',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.REJECT_VOUCHER]: 'Reject Voucher',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.RETURN_VOUCHER]: 'Return Voucher',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.DISPATCH_TO_FINANCE]: 'Dispatch to Finance',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.RESUBMIT_FROM_REJECTED]: 'Resubmit from Rejected',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.RESUBMIT_FROM_RETURNED]: 'Resubmit from Returned',
    };
    return actionNames[event] || event;
}
function requiresConfirmation(event) {
    return [
        VoucherWorkflowStateMachine_1.WorkflowEvent.REJECT_VOUCHER,
        VoucherWorkflowStateMachine_1.WorkflowEvent.RETURN_VOUCHER,
        VoucherWorkflowStateMachine_1.WorkflowEvent.DISPATCH_TO_FINANCE
    ].includes(event);
}
function getActionIcon(event) {
    const icons = {
        [VoucherWorkflowStateMachine_1.WorkflowEvent.CREATE_VOUCHER]: 'plus',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.SUBMIT_TO_AUDIT]: 'send',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.RECEIVE_FROM_FINANCE]: 'inbox',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.START_WORK]: 'play',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.CERTIFY_VOUCHER]: 'check-circle',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.REJECT_VOUCHER]: 'x-circle',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.RETURN_VOUCHER]: 'arrow-left',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.DISPATCH_TO_FINANCE]: 'arrow-right',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.RESUBMIT_FROM_REJECTED]: 'refresh-cw',
        [VoucherWorkflowStateMachine_1.WorkflowEvent.RESUBMIT_FROM_RETURNED]: 'refresh-cw',
    };
    return icons[event] || 'circle';
}
exports.default = router;

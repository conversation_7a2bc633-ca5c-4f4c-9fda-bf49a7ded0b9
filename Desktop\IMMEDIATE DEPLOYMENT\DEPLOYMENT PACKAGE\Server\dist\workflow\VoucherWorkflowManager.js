"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoucherWorkflowManager = void 0;
const VoucherWorkflowStateMachine_1 = require("./VoucherWorkflowStateMachine");
const CircuitBreaker_js_1 = require("../utils/CircuitBreaker.js");
const EventDeduplicator_js_1 = require("../utils/EventDeduplicator.js");
const logger_js_1 = require("../utils/logger.js");
class VoucherWorkflowManager {
    db;
    eventPublisher;
    auditLogger;
    constructor(db, eventPublisher, auditLogger) {
        this.db = db;
        this.eventPublisher = eventPublisher;
        this.auditLogger = auditLogger;
    }
    async transitionVoucher(eventData) {
        const { type: event, voucherId, context } = eventData;
        const eventKey = {
            type: `workflow_transition_${event}`,
            entityId: voucherId,
            userId: context.userId
        };
        if (!EventDeduplicator_js_1.eventDeduplicator.shouldProcess(eventKey)) {
            logger_js_1.logger.warn(`🚫 WORKFLOW PROTECTION: Blocked duplicate transition for voucher ${voucherId}`);
            throw new Error('Duplicate workflow transition blocked');
        }
        return await CircuitBreaker_js_1.circuitBreakerManager.execute('workflow_transition', async () => {
            return await this.withTransaction(async (tx) => {
                let currentState = VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_PENDING;
                try {
                    const currentVoucher = await this.getVoucherByIdWithRetry(voucherId, tx);
                    if (!currentVoucher) {
                        throw new Error(`Voucher ${voucherId} not found`);
                    }
                    currentState = currentVoucher.workflow_state;
                    const transition = VoucherWorkflowStateMachine_1.VoucherWorkflowStateMachine.getTransition(currentState, event);
                    if (!transition) {
                        const validEvents = VoucherWorkflowStateMachine_1.VoucherWorkflowStateMachine.getValidEvents(currentState);
                        throw new Error(`Invalid transition from ${currentState} with event ${event}. Valid events: ${validEvents.join(', ')}`);
                    }
                    const newState = transition.toState;
                    const badge = transition.badge || VoucherWorkflowStateMachine_1.BadgeType.NONE;
                    const affectedTabs = this.getAffectedTabs(currentState, newState, context);
                    let copy = null;
                    if (transition.requiresCopy && transition.copyState) {
                        copy = await this.createVoucherCopy(currentVoucher, transition.copyState, tx);
                    }
                    const updatedVoucher = await this.updateVoucherState(voucherId, {
                        workflow_state: newState,
                        badge_type: badge,
                        department: this.getDepartmentForState(newState, currentVoucher),
                        version: currentVoucher.version + 1,
                        last_modified: new Date()
                    }, tx);
                    await this.logWorkflowTransition({
                        voucherId,
                        fromState: currentState,
                        toState: newState,
                        event,
                        userId: context.userId,
                        timestamp: context.timestamp,
                        copyId: copy?.id
                    }, tx);
                    await this.publishWorkflowEvent({
                        type: 'VOUCHER_STATE_CHANGED',
                        voucherId,
                        previousState: currentState,
                        newState,
                        affectedDepartments: [currentVoucher.original_department, 'AUDIT'],
                        affectedTabs,
                        copy
                    });
                    EventDeduplicator_js_1.eventDeduplicator.markProcessed(eventKey);
                    return {
                        success: true,
                        voucher: updatedVoucher,
                        copy,
                        previousState: currentState,
                        newState,
                        affectedTabs
                    };
                }
                catch (error) {
                    logger_js_1.logger.error(`Workflow transition failed for voucher ${voucherId}:`, error);
                    if (error instanceof Error) {
                        await this.handleWorkflowError(voucherId, currentState, event, error, tx);
                    }
                    throw error;
                }
            });
        }, {
            failureThreshold: 3,
            recoveryTimeout: 30000,
            monitoringPeriod: 60000
        });
    }
    async createVoucherCopy(originalVoucher, copyState, tx) {
        const copyData = {
            ...originalVoucher,
            id: this.generateId(),
            voucher_id: `${originalVoucher.voucher_id}_COPY`,
            workflow_state: copyState,
            is_copy: true,
            parent_voucher_id: originalVoucher.id,
            created_at: new Date(),
            version: 1
        };
        delete copyData.version;
        const columns = Object.keys(copyData).join(', ');
        const placeholders = Object.keys(copyData).map(() => '?').join(', ');
        const values = Object.values(copyData);
        const query = `
      INSERT INTO vouchers (${columns}) VALUES (${placeholders})
    `;
        await tx.execute(query, values);
        return copyData;
    }
    async updateVoucherState(voucherId, updates, tx) {
        const setClause = Object.keys(updates)
            .map(key => `${key} = ?`)
            .join(', ');
        const query = `
      UPDATE vouchers 
      SET ${setClause}
      WHERE id = ?
    `;
        const values = [...Object.values(updates), voucherId];
        await tx.execute(query, values);
        return await this.getVoucherById(voucherId, tx);
    }
    async getVoucherById(voucherId, tx) {
        const query = `
      SELECT * FROM vouchers WHERE id = ?
    `;
        if (tx) {
            const [rows] = await tx.execute(query, [voucherId]);
            return rows[0] || null;
        }
        else {
            const [rows] = await this.db.execute(query, [voucherId]);
            return rows[0] || null;
        }
    }
    getDepartmentForState(state, voucher) {
        if (state.startsWith('AUDIT_')) {
            return 'AUDIT';
        }
        if (state.startsWith('FINANCE_')) {
            return voucher.original_department;
        }
        return voucher.department;
    }
    getAffectedTabs(fromState, toState, context) {
        const tabs = [];
        const fromTab = VoucherWorkflowStateMachine_1.VoucherWorkflowStateMachine.getTabForVoucher(fromState, context.userDepartment);
        const toTab = VoucherWorkflowStateMachine_1.VoucherWorkflowStateMachine.getTabForVoucher(toState, context.userDepartment);
        if (fromTab)
            tabs.push(fromTab);
        if (toTab && toTab !== fromTab)
            tabs.push(toTab);
        const auditFromTab = VoucherWorkflowStateMachine_1.VoucherWorkflowStateMachine.getTabForVoucher(fromState, 'AUDIT');
        const auditToTab = VoucherWorkflowStateMachine_1.VoucherWorkflowStateMachine.getTabForVoucher(toState, 'AUDIT');
        if (auditFromTab && !tabs.includes(auditFromTab))
            tabs.push(auditFromTab);
        if (auditToTab && !tabs.includes(auditToTab))
            tabs.push(auditToTab);
        return tabs;
    }
    async logWorkflowTransition(data, tx) {
        const query = `
      INSERT INTO workflow_audit_log 
      (voucher_id, from_state, to_state, event_type, user_id, timestamp, copy_id)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;
        await tx.execute(query, [
            data.voucherId,
            data.fromState,
            data.toState,
            data.event,
            data.userId,
            data.timestamp,
            data.copyId || null
        ]);
    }
    async publishWorkflowEvent(eventData) {
        if (this.eventPublisher) {
            await this.eventPublisher.publish('workflow.state.changed', eventData);
        }
    }
    async withTransaction(fn) {
        const connection = await this.db.getConnection();
        await connection.beginTransaction();
        try {
            const result = await fn(connection);
            await connection.commit();
            return result;
        }
        catch (error) {
            await connection.rollback();
            throw error;
        }
        finally {
            connection.release();
        }
    }
    generateId() {
        return require('uuid').v4();
    }
    async getVoucherByIdWithRetry(voucherId, tx, maxRetries = 3) {
        let lastError = null;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await this.getVoucherById(voucherId, tx);
            }
            catch (error) {
                lastError = error;
                logger_js_1.logger.warn(`Voucher retrieval attempt ${attempt}/${maxRetries} failed for ${voucherId}:`, error);
                if (attempt < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
                }
            }
        }
        throw lastError || new Error(`Failed to retrieve voucher ${voucherId} after ${maxRetries} attempts`);
    }
    async handleWorkflowError(voucherId, currentState, event, error, tx) {
        try {
            logger_js_1.logger.error(`🚨 WORKFLOW ERROR RECOVERY: Voucher ${voucherId}`, {
                currentState,
                event,
                error: error.message,
                timestamp: new Date().toISOString()
            });
            if (error.message.includes('Invalid transition')) {
                await this.attemptStateRecovery(voucherId, currentState, tx);
            }
            else if (error.message.includes('not found')) {
                await this.attemptVoucherRecovery(voucherId, tx);
            }
        }
        catch (recoveryError) {
            logger_js_1.logger.error(`🚨 WORKFLOW ERROR RECOVERY FAILED for voucher ${voucherId}:`, recoveryError);
        }
    }
    async attemptStateRecovery(voucherId, currentState, tx) {
        logger_js_1.logger.info(`🔧 ATTEMPTING STATE RECOVERY for voucher ${voucherId} in state ${currentState}`);
    }
    async attemptVoucherRecovery(voucherId, tx) {
        logger_js_1.logger.info(`🔧 ATTEMPTING VOUCHER RECOVERY for ${voucherId}`);
    }
}
exports.VoucherWorkflowManager = VoucherWorkflowManager;

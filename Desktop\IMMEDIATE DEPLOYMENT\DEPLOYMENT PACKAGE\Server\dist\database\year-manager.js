"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.yearDatabaseManager = void 0;
exports.getYearFromSession = getYearFromSession;
exports.getDatabaseFromSession = getDatabaseFromSession;
const promise_1 = __importDefault(require("mysql2/promise"));
const logger_js_1 = require("../utils/logger.js");
class YearDatabaseManager {
    pools = new Map();
    baseConfig;
    constructor() {
        this.baseConfig = {
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT || '3306'),
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || 'vms@2025@1989',
            database: process.env.DB_NAME || 'vms_production',
            waitForConnections: true,
            connectionLimit: 10,
            queueLimit: 0
        };
    }
    getYearDatabase(year) {
        const currentYear = new Date().getFullYear();
        if (year === currentYear) {
            return this.baseConfig.database;
        }
        return `vms_${year}`;
    }
    async getYearPool(year) {
        const dbName = this.getYearDatabase(year);
        if (this.pools.has(dbName)) {
            return this.pools.get(dbName);
        }
        const config = {
            ...this.baseConfig,
            database: dbName
        };
        const pool = promise_1.default.createPool(config);
        this.pools.set(dbName, pool);
        logger_js_1.logger.info(`Created connection pool for year ${year} (database: ${dbName})`);
        return pool;
    }
    async queryYear(year, sql, params = []) {
        try {
            const pool = await this.getYearPool(year);
            const [results] = await pool.execute(sql, params);
            return results;
        }
        catch (error) {
            logger_js_1.logger.error(`Error executing query on year ${year}:`, error);
            throw error;
        }
    }
    async queryWithSession(session, sql, params = []) {
        const year = session?.selectedYear || new Date().getFullYear();
        return this.queryYear(year, sql, params);
    }
    async yearDatabaseExists(year) {
        try {
            const dbName = this.getYearDatabase(year);
            const pool = await this.getYearPool(new Date().getFullYear());
            const [results] = await pool.execute('SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?', [dbName]);
            return Array.isArray(results) && results.length > 0;
        }
        catch (error) {
            logger_js_1.logger.error(`Error checking if year ${year} database exists:`, error);
            return false;
        }
    }
    async getAvailableYears() {
        try {
            const pool = await this.getYearPool(new Date().getFullYear());
            const [results] = await pool.execute('SHOW DATABASES');
            const years = [];
            for (const row of results) {
                const dbName = row.Database;
                if (dbName.startsWith('vms_') && dbName.match(/vms_\d{4}$/)) {
                    const year = parseInt(dbName.replace('vms_', ''));
                    if (!isNaN(year)) {
                        years.push(year);
                    }
                }
            }
            const currentYear = new Date().getFullYear();
            if (!years.includes(currentYear)) {
                years.push(currentYear);
            }
            return years.sort((a, b) => b - a);
        }
        catch (error) {
            logger_js_1.logger.error('Error getting available years:', error);
            return [new Date().getFullYear()];
        }
    }
    async createYearDatabase(year) {
        try {
            const dbName = this.getYearDatabase(year);
            const pool = await this.getYearPool(new Date().getFullYear());
            await pool.execute(`CREATE DATABASE IF NOT EXISTS ${dbName}`);
            const newPool = await this.getYearPool(year);
            const currentDb = this.baseConfig.database;
            const tables = [
                'users', 'vouchers', 'voucher_batches', 'batch_vouchers',
                'provisional_cash_records', 'notifications', 'audit_logs',
                'voucher_logs', 'active_sessions'
            ];
            for (const table of tables) {
                try {
                    const [createResults] = await pool.execute(`SHOW CREATE TABLE ${currentDb}.${table}`);
                    if (createResults.length > 0) {
                        const createSQL = createResults[0]['Create Table'];
                        await newPool.execute(createSQL);
                    }
                }
                catch (tableError) {
                    logger_js_1.logger.warn(`Could not copy table ${table} to year ${year}:`, tableError);
                }
            }
            await newPool.execute(`INSERT IGNORE INTO users SELECT * FROM ${currentDb}.users`);
            logger_js_1.logger.info(`Successfully created year database: ${dbName}`);
            return true;
        }
        catch (error) {
            logger_js_1.logger.error(`Error creating year ${year} database:`, error);
            return false;
        }
    }
    async closeAll() {
        const closePromises = Array.from(this.pools.values()).map(pool => pool.end());
        await Promise.all(closePromises);
        this.pools.clear();
        logger_js_1.logger.info('Closed all year database connection pools');
    }
    async getYearStatistics(year) {
        try {
            const exists = await this.yearDatabaseExists(year);
            if (!exists) {
                return {
                    year,
                    voucherCount: 0,
                    totalAmount: 0,
                    departments: [],
                    isActive: false,
                    lastActivity: new Date().toISOString()
                };
            }
            const voucherStats = await this.queryYear(year, `
        SELECT 
          COUNT(*) as voucher_count,
          COALESCE(SUM(amount), 0) as total_amount
        FROM vouchers
      `);
            const departments = await this.queryYear(year, `
        SELECT DISTINCT department 
        FROM vouchers 
        WHERE department IS NOT NULL
        ORDER BY department
      `);
            const lastActivity = await this.queryYear(year, `
        SELECT MAX(created_at) as last_activity 
        FROM vouchers
      `);
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            const recentActivity = await this.queryYear(year, `
        SELECT COUNT(*) as recent_count 
        FROM vouchers 
        WHERE created_at >= ?
      `, [thirtyDaysAgo.toISOString()]);
            return {
                year,
                voucherCount: voucherStats[0]?.voucher_count || 0,
                totalAmount: parseFloat(voucherStats[0]?.total_amount || '0'),
                departments: departments.map((d) => d.department),
                isActive: (recentActivity[0]?.recent_count || 0) > 0,
                lastActivity: lastActivity[0]?.last_activity || new Date().toISOString()
            };
        }
        catch (error) {
            logger_js_1.logger.error(`Error getting statistics for year ${year}:`, error);
            return {
                year,
                voucherCount: 0,
                totalAmount: 0,
                departments: [],
                isActive: false,
                lastActivity: new Date().toISOString()
            };
        }
    }
}
exports.yearDatabaseManager = new YearDatabaseManager();
function getYearFromSession(session) {
    return session?.selectedYear || new Date().getFullYear();
}
function getDatabaseFromSession(session) {
    const year = getYearFromSession(session);
    const currentYear = new Date().getFullYear();
    if (year === currentYear) {
        return process.env.DB_NAME || 'vms_production';
    }
    return `vms_${year}`;
}

"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.networkDiscoveryService = exports.NetworkDiscoveryService = void 0;
const dgram_1 = __importDefault(require("dgram"));
const os_1 = __importDefault(require("os"));
const logger_js_1 = require("../utils/logger.js");
class NetworkDiscoveryService {
    broadcastSocket = null;
    discoverySocket = null;
    announceTimer = null;
    isRunning = false;
    config = {
        broadcastPort: 8081,
        discoveryPort: 8082,
        serviceName: 'VMS-Production',
        announceInterval: 30000,
        enabled: true
    };
    serverInfo;
    constructor(serverPort = 8080) {
        this.serverInfo = {
            serviceName: this.config.serviceName,
            serverIP: this.getLocalIPAddress(),
            serverPort: serverPort,
            version: '1.0.0',
            timestamp: Date.now(),
            capabilities: ['voucher-management', 'real-time-updates', 'multi-user', 'automated-deployment'],
            deploymentMode: process.env.VMS_DEPLOYMENT_MODE || 'standard'
        };
        logger_js_1.logger.info(`Network Discovery Service initialized for ${this.serverInfo.serverIP}:${serverPort}`);
    }
    async start() {
        try {
            if (this.isRunning) {
                logger_js_1.logger.warn('Network Discovery Service is already running');
                return;
            }
            logger_js_1.logger.info('🌐 Starting Network Discovery Service...');
            await this.startBroadcastSocket();
            await this.startDiscoverySocket();
            this.startPeriodicAnnouncements();
            this.isRunning = true;
            logger_js_1.logger.info(`✅ Network Discovery Service started successfully`);
            logger_js_1.logger.info(`📡 Broadcasting on port ${this.config.broadcastPort}`);
            logger_js_1.logger.info(`🔍 Discovery listening on port ${this.config.discoveryPort}`);
            logger_js_1.logger.info(`🖥️ Server announced as: ${this.serverInfo.serverIP}:${this.serverInfo.serverPort}`);
        }
        catch (error) {
            logger_js_1.logger.error('❌ Failed to start Network Discovery Service:', error);
            throw error;
        }
    }
    async stop() {
        try {
            if (!this.isRunning) {
                return;
            }
            logger_js_1.logger.info('🛑 Stopping Network Discovery Service...');
            if (this.announceTimer) {
                clearInterval(this.announceTimer);
                this.announceTimer = null;
            }
            if (this.broadcastSocket) {
                this.broadcastSocket.close();
                this.broadcastSocket = null;
            }
            if (this.discoverySocket) {
                this.discoverySocket.close();
                this.discoverySocket = null;
            }
            this.isRunning = false;
            logger_js_1.logger.info('✅ Network Discovery Service stopped');
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error stopping Network Discovery Service:', error);
        }
    }
    async startBroadcastSocket() {
        return new Promise((resolve, reject) => {
            try {
                this.broadcastSocket = dgram_1.default.createSocket('udp4');
                this.broadcastSocket.on('error', (error) => {
                    logger_js_1.logger.error('Broadcast socket error:', error);
                    reject(error);
                });
                this.broadcastSocket.bind(() => {
                    this.broadcastSocket.setBroadcast(true);
                    logger_js_1.logger.info(`📡 Broadcast socket bound and ready`);
                    resolve();
                });
            }
            catch (error) {
                reject(error);
            }
        });
    }
    async startDiscoverySocket() {
        return new Promise((resolve, reject) => {
            try {
                this.discoverySocket = dgram_1.default.createSocket('udp4');
                this.discoverySocket.on('error', (error) => {
                    logger_js_1.logger.error('Discovery socket error:', error);
                    reject(error);
                });
                this.discoverySocket.on('message', (message, remoteInfo) => {
                    this.handleDiscoveryRequest(message, remoteInfo);
                });
                this.discoverySocket.bind(this.config.discoveryPort, () => {
                    logger_js_1.logger.info(`🔍 Discovery socket listening on port ${this.config.discoveryPort}`);
                    resolve();
                });
            }
            catch (error) {
                reject(error);
            }
        });
    }
    handleDiscoveryRequest(message, remoteInfo) {
        try {
            const request = JSON.parse(message.toString());
            if (request.type === 'VMS_DISCOVERY_REQUEST') {
                logger_js_1.logger.info(`🔍 Discovery request from ${remoteInfo.address}:${remoteInfo.port}`);
                this.serverInfo.timestamp = Date.now();
                this.serverInfo.serverIP = this.getLocalIPAddress();
                const response = {
                    type: 'VMS_DISCOVERY_RESPONSE',
                    server: this.serverInfo,
                    respondedAt: Date.now()
                };
                const responseBuffer = Buffer.from(JSON.stringify(response));
                this.discoverySocket.send(responseBuffer, remoteInfo.port, remoteInfo.address, (error) => {
                    if (error) {
                        logger_js_1.logger.error('Error sending discovery response:', error);
                    }
                    else {
                        logger_js_1.logger.info(`📤 Discovery response sent to ${remoteInfo.address}:${remoteInfo.port}`);
                    }
                });
            }
        }
        catch (error) {
            logger_js_1.logger.warn('Invalid discovery request received:', error);
        }
    }
    startPeriodicAnnouncements() {
        this.broadcastServerAnnouncement();
        this.announceTimer = setInterval(() => {
            this.broadcastServerAnnouncement();
        }, this.config.announceInterval);
        logger_js_1.logger.info(`⏰ Periodic announcements scheduled every ${this.config.announceInterval / 1000} seconds`);
    }
    broadcastServerAnnouncement() {
        try {
            if (!this.broadcastSocket) {
                return;
            }
            this.serverInfo.timestamp = Date.now();
            this.serverInfo.serverIP = this.getLocalIPAddress();
            const announcement = {
                type: 'VMS_SERVER_ANNOUNCEMENT',
                server: this.serverInfo
            };
            const message = Buffer.from(JSON.stringify(announcement));
            const broadcastAddresses = this.getBroadcastAddresses();
            broadcastAddresses.forEach(address => {
                this.broadcastSocket.send(message, this.config.broadcastPort, address, (error) => {
                    if (error) {
                        logger_js_1.logger.debug(`Broadcast error to ${address}:`, error.message);
                    }
                    else {
                        logger_js_1.logger.debug(`📡 Server announced to ${address}:${this.config.broadcastPort}`);
                    }
                });
            });
        }
        catch (error) {
            logger_js_1.logger.error('Error broadcasting server announcement:', error);
        }
    }
    getLocalIPAddress() {
        try {
            const interfaces = os_1.default.networkInterfaces();
            const preferredTypes = ['Ethernet', 'Wi-Fi', 'WiFi', 'Wireless'];
            for (const type of preferredTypes) {
                for (const [name, addresses] of Object.entries(interfaces)) {
                    if (name.toLowerCase().includes(type.toLowerCase()) && addresses) {
                        for (const addr of addresses) {
                            if (addr.family === 'IPv4' && !addr.internal && addr.address !== '127.0.0.1') {
                                return addr.address;
                            }
                        }
                    }
                }
            }
            for (const addresses of Object.values(interfaces)) {
                if (addresses) {
                    for (const addr of addresses) {
                        if (addr.family === 'IPv4' && !addr.internal && addr.address !== '127.0.0.1') {
                            return addr.address;
                        }
                    }
                }
            }
            return 'localhost';
        }
        catch (error) {
            logger_js_1.logger.warn('Error getting local IP address:', error);
            return 'localhost';
        }
    }
    getBroadcastAddresses() {
        try {
            const interfaces = os_1.default.networkInterfaces();
            const broadcastAddresses = [];
            for (const addresses of Object.values(interfaces)) {
                if (addresses) {
                    for (const addr of addresses) {
                        if (addr.family === 'IPv4' && !addr.internal) {
                            const ip = addr.address.split('.').map(Number);
                            const netmask = addr.netmask.split('.').map(Number);
                            const broadcast = ip.map((octet, index) => octet | (255 - netmask[index])).join('.');
                            if (!broadcastAddresses.includes(broadcast)) {
                                broadcastAddresses.push(broadcast);
                            }
                        }
                    }
                }
            }
            if (broadcastAddresses.length === 0) {
                broadcastAddresses.push('***************');
            }
            return broadcastAddresses;
        }
        catch (error) {
            logger_js_1.logger.warn('Error getting broadcast addresses:', error);
            return ['***************'];
        }
    }
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        logger_js_1.logger.info('Network Discovery Service configuration updated');
    }
    getServerInfo() {
        return { ...this.serverInfo };
    }
    isServiceRunning() {
        return this.isRunning;
    }
    getNetworkStats() {
        return {
            isRunning: this.isRunning,
            serverIP: this.serverInfo.serverIP,
            serverPort: this.serverInfo.serverPort,
            broadcastPort: this.config.broadcastPort,
            discoveryPort: this.config.discoveryPort,
            announceInterval: this.config.announceInterval,
            lastAnnouncement: this.serverInfo.timestamp,
            broadcastAddresses: this.getBroadcastAddresses()
        };
    }
}
exports.NetworkDiscoveryService = NetworkDiscoveryService;
exports.networkDiscoveryService = new NetworkDiscoveryService();

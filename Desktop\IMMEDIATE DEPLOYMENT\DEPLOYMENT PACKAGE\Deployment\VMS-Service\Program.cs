using System;
using System.ServiceProcess;
using System.Reflection;
using System.IO;
using System.Diagnostics;

namespace VMSWindowsService
{
    /// <summary>
    /// Main program entry point for VMS Windows Service
    /// Handles service installation, uninstallation, and execution
    /// </summary>
    static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        static void Main(string[] args)
        {
            try
            {
                // Check for command line arguments
                if (args.Length > 0)
                {
                    switch (args[0].ToLower())
                    {
                        case "/install":
                        case "-install":
                            InstallService();
                            return;
                            
                        case "/uninstall":
                        case "-uninstall":
                            UninstallService();
                            return;
                            
                        case "/console":
                        case "-console":
                            RunAsConsole();
                            return;
                            
                        case "/help":
                        case "-help":
                        case "/?":
                            ShowHelp();
                            return;
                            
                        default:
                            Console.WriteLine($"Unknown argument: {args[0]}");
                            ShowHelp();
                            return;
                    }
                }

                // Run as Windows Service (default)
                ServiceBase[] ServicesToRun;
                ServicesToRun = new ServiceBase[]
                {
                    new VMSWindowsService()
                };
                ServiceBase.Run(ServicesToRun);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                LogError($"Service startup error: {ex}");
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// Install the Windows Service using sc.exe command
        /// </summary>
        private static void InstallService()
        {
            try
            {
                Console.WriteLine("Installing VMS Windows Service...");

                string servicePath = Assembly.GetExecutingAssembly().Location;
                string serviceName = "VMS-Production-Service";
                string displayName = "VMS Production Service";
                string description = "Automated VMS system management service for production deployment";

                // Use sc.exe to create the service
                var createProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "sc.exe",
                        Arguments = $"create \"{serviceName}\" binPath=\"{servicePath}\" DisplayName=\"{displayName}\" start=auto",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                createProcess.Start();
                string output = createProcess.StandardOutput.ReadToEnd();
                string error = createProcess.StandardError.ReadToEnd();
                createProcess.WaitForExit();

                if (createProcess.ExitCode != 0)
                {
                    throw new Exception($"Failed to create service: {error}");
                }

                // Set service description
                var descProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "sc.exe",
                        Arguments = $"description \"{serviceName}\" \"{description}\"",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                descProcess.Start();
                descProcess.WaitForExit();

                Console.WriteLine("VMS Windows Service installed successfully!");
                Console.WriteLine("Service will start automatically on system boot.");
                Console.WriteLine($"To start the service now, run: net start {serviceName}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error installing service: {ex.Message}");
                LogError($"Service installation error: {ex}");
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// Uninstall the Windows Service using sc.exe command
        /// </summary>
        private static void UninstallService()
        {
            try
            {
                Console.WriteLine("Uninstalling VMS Windows Service...");

                string serviceName = "VMS-Production-Service";

                // Stop the service first if it's running
                var stopProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "sc.exe",
                        Arguments = $"stop \"{serviceName}\"",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                stopProcess.Start();
                stopProcess.WaitForExit();

                // Wait a moment for the service to stop
                System.Threading.Thread.Sleep(2000);

                // Delete the service
                var deleteProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "sc.exe",
                        Arguments = $"delete \"{serviceName}\"",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                deleteProcess.Start();
                string output = deleteProcess.StandardOutput.ReadToEnd();
                string error = deleteProcess.StandardError.ReadToEnd();
                deleteProcess.WaitForExit();

                if (deleteProcess.ExitCode != 0)
                {
                    throw new Exception($"Failed to delete service: {error}");
                }

                Console.WriteLine("VMS Windows Service uninstalled successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error uninstalling service: {ex.Message}");
                LogError($"Service uninstallation error: {ex}");
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// Run service as console application for testing
        /// </summary>
        private static void RunAsConsole()
        {
            try
            {
                Console.WriteLine("Running VMS Service in console mode...");
                Console.WriteLine("Press Ctrl+C to stop the service.");
                
                var service = new VMSWindowsService();
                
                // Use reflection to call protected OnStart method
                var onStartMethod = typeof(ServiceBase).GetMethod("OnStart", 
                    BindingFlags.NonPublic | BindingFlags.Instance);
                onStartMethod?.Invoke(service, new object[] { new string[0] });
                
                Console.WriteLine("VMS Service started. Press any key to stop...");
                Console.ReadKey();
                
                // Use reflection to call protected OnStop method
                var onStopMethod = typeof(ServiceBase).GetMethod("OnStop", 
                    BindingFlags.NonPublic | BindingFlags.Instance);
                onStopMethod?.Invoke(service, null);
                
                Console.WriteLine("VMS Service stopped.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error running service in console mode: {ex.Message}");
                LogError($"Console mode error: {ex}");
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// Show help information
        /// </summary>
        private static void ShowHelp()
        {
            Console.WriteLine("VMS Windows Service - Automated Production Deployment");
            Console.WriteLine("====================================================");
            Console.WriteLine();
            Console.WriteLine("Usage:");
            Console.WriteLine("  VMS-WindowsService.exe [option]");
            Console.WriteLine();
            Console.WriteLine("Options:");
            Console.WriteLine("  /install    Install the Windows Service");
            Console.WriteLine("  /uninstall  Uninstall the Windows Service");
            Console.WriteLine("  /console    Run as console application (for testing)");
            Console.WriteLine("  /help       Show this help information");
            Console.WriteLine();
            Console.WriteLine("Default behavior (no arguments): Run as Windows Service");
            Console.WriteLine();
            Console.WriteLine("Features:");
            Console.WriteLine("  • Automatic startup on system boot");
            Console.WriteLine("  • Port 8080 reservation and management");
            Console.WriteLine("  • VMS server process monitoring and auto-restart");
            Console.WriteLine("  • Dynamic VMS path detection");
            Console.WriteLine("  • Health monitoring and recovery");
            Console.WriteLine("  • Comprehensive logging");
            Console.WriteLine();
            Console.WriteLine("Installation Steps:");
            Console.WriteLine("  1. Run as Administrator: VMS-WindowsService.exe /install");
            Console.WriteLine("  2. Start service: net start VMS-Production-Service");
            Console.WriteLine("  3. Service will auto-start on future system boots");
            Console.WriteLine();
            Console.WriteLine("Logs Location: [VMS-Path]\\Logs\\service.log");
        }

        /// <summary>
        /// Log error to file
        /// </summary>
        private static void LogError(string message)
        {
            try
            {
                string logPath = Path.Combine(
                    Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? "C:\\",
                    "service-error.log"
                );
                
                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}";
                File.AppendAllText(logPath, logEntry + Environment.NewLine);
            }
            catch
            {
                // Ignore logging errors
            }
        }
    }
}

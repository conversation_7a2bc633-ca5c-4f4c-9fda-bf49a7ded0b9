"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authRouter = void 0;
const express_1 = __importDefault(require("express"));
const uuid_1 = require("uuid");
const db_js_1 = require("../database/db.js");
const auth_js_1 = require("../middleware/auth.js");
const logger_js_1 = require("../utils/logger.js");
const socketHandlers_js_1 = require("../socket/socketHandlers.js");
const audit_service_js_1 = require("../services/audit-service.js");
exports.authRouter = express_1.default.Router();
exports.authRouter.post('/login', async (req, res) => {
    try {
        const { department, username, password } = req.body;
        if (!department || !username || !password) {
            return res.status(400).json({
                error: 'Please enter department, username, and password'
            });
        }
        const normalizedDepartment = department.toUpperCase().trim();
        const normalizedUsername = username.toUpperCase().trim();
        const users = await (0, db_js_1.query)(`
      SELECT id, name, password, department, role, is_active, last_login
      FROM users
      WHERE UPPER(TRIM(name)) = ? AND UPPER(TRIM(department)) = ? AND is_active = 1
    `, [normalizedUsername, normalizedDepartment]);
        if (users.length === 0) {
            logger_js_1.logger.warn(`Login attempt failed - user not found: ${normalizedUsername} (${normalizedDepartment})`);
            return res.status(401).json({
                error: 'Username, department, or password incorrect. Please check and try again.'
            });
        }
        const user = users[0];
        if (password !== user.password) {
            logger_js_1.logger.warn(`Login attempt failed - wrong password: ${user.name} (${user.department})`);
            return res.status(401).json({
                error: 'Username, department, or password incorrect. Please check and try again.'
            });
        }
        await (0, db_js_1.query)('UPDATE users SET last_login = NOW() WHERE id = ?', [user.id]);
        logger_js_1.logger.info(`✅ Successful login: ${user.name} (${user.department}) - Role: ${user.role}`);
        try {
            await audit_service_js_1.AuditService.logLogin(user.id, user.name, user.department, req.ip || 'unknown', req.get('User-Agent') || 'unknown');
        }
        catch (auditError) {
            logger_js_1.logger.warn('Failed to log login audit:', auditError);
        }
        await (0, db_js_1.query)('DELETE FROM active_sessions WHERE is_active = FALSE AND session_end < DATE_SUB(NOW(), INTERVAL 7 DAY)');
        const sessionId = (0, uuid_1.v4)();
        await (0, db_js_1.query)(`INSERT INTO active_sessions (id, user_id, user_name, department, session_start, last_activity, is_active)
       VALUES (?, ?, ?, ?, NOW(), NOW(), TRUE)`, [sessionId, user.id, user.name, user.department]);
        logger_js_1.logger.info(`Created new session for user ${user.name} (session: ${sessionId})`);
        await (0, db_js_1.query)(`UPDATE active_sessions SET is_active = FALSE, session_end = NOW()
       WHERE user_id = ? AND is_active = TRUE AND session_start < DATE_SUB(NOW(), INTERVAL 30 DAY)`, [user.id]);
        res.cookie('vms_session_id', sessionId, {
            httpOnly: true,
            secure: false,
            maxAge: 8 * 60 * 60 * 1000,
            sameSite: 'lax'
        });
        return res.json({
            success: true,
            sessionId: sessionId,
            user: {
                id: user.id,
                name: user.name,
                department: user.department,
                role: user.role,
                lastLogin: new Date().toISOString()
            },
            message: 'Login successful - Welcome to VMS'
        });
    }
    catch (error) {
        logger_js_1.logger.error('Login system error:', error);
        return res.status(500).json({
            error: 'System error. Please try again or contact IT support.'
        });
    }
});
exports.authRouter.post('/register', async (req, res) => {
    try {
        const { name, password, department } = req.body;
        if (!name || !password || !department) {
            return res.status(400).json({ error: 'Name, password, and department are required' });
        }
        const registrationId = (0, uuid_1.v4)();
        const normalizedName = name.toUpperCase();
        const normalizedDepartment = department.toUpperCase();
        console.log(`Storing password directly: ${password}`);
        console.log(`Creating new registration: ${normalizedName} (${normalizedDepartment}) with ID: ${registrationId}`);
        await (0, db_js_1.query)('INSERT INTO pending_registrations (id, name, password, department, date_requested, status) VALUES (?, ?, ?, ?, NOW(), ?)', [registrationId, normalizedName, password, normalizedDepartment, 'pending']);
        const notificationId = (0, uuid_1.v4)();
        await (0, db_js_1.query)('INSERT INTO notifications (id, user_id, message, is_read, timestamp, type) VALUES (?, ?, ?, ?, NOW(), ?)', [notificationId, 'admin', `New user registration: ${normalizedName} (${normalizedDepartment})`, false, 'OTHER']);
        const newRegistration = {
            id: registrationId,
            name: normalizedName,
            department: normalizedDepartment,
            dateRequested: new Date().toISOString(),
            status: 'pending'
        };
        (0, socketHandlers_js_1.broadcastRegistrationUpdate)('created', newRegistration);
        console.log(`Broadcasting new registration: ${registrationId}`);
        const verifyRegistrations = await (0, db_js_1.query)('SELECT * FROM pending_registrations WHERE id = ?', [registrationId]);
        console.log(`Verification: Found ${verifyRegistrations.length} registrations with ID ${registrationId}`);
        res.status(201).json({
            message: 'Registration request submitted successfully',
            registrationId
        });
    }
    catch (error) {
        logger_js_1.logger.error('Registration error:', error);
        res.status(500).json({ error: 'Registration failed' });
    }
});
exports.authRouter.get('/me', auth_js_1.authenticate, async (req, res) => {
    try {
        const userId = req.user.id;
        const users = await (0, db_js_1.query)('SELECT * FROM users WHERE id = ?', [userId]);
        if (users.length === 0) {
            return res.status(404).json({ error: 'User not found' });
        }
        const user = users[0];
        res.json({
            id: user.id,
            name: user.name,
            department: user.department,
            role: user.role,
            lastLogin: user.last_login,
            dateCreated: user.date_created,
            isActive: Boolean(user.is_active)
        });
    }
    catch (error) {
        logger_js_1.logger.error('Get current user error:', error);
        res.status(500).json({ error: 'Failed to get user data' });
    }
});
exports.authRouter.post('/logout', auth_js_1.authenticate, async (req, res) => {
    try {
        const sessionId = req.user.sessionId;
        if (!sessionId) {
            logger_js_1.logger.warn(`User ${req.user.name} (${req.user.id}) attempted to logout without a valid session ID`);
            return res.status(400).json({ error: 'No active session found' });
        }
        try {
            const { releaseUserLocksOnLogout } = require('../socket/socketHandlers');
            const releasedLocks = releaseUserLocksOnLogout(req.user.id);
            if (releasedLocks > 0) {
                logger_js_1.logger.info(`🧹 Logout cleanup: Released ${releasedLocks} resource locks for user ${req.user.name} (${req.user.id})`);
            }
        }
        catch (error) {
            logger_js_1.logger.warn('Failed to cleanup resource locks on logout:', error);
        }
        await (0, db_js_1.query)('UPDATE active_sessions SET is_active = FALSE, session_end = NOW() WHERE id = ?', [sessionId]);
        try {
            await audit_service_js_1.AuditService.logLogout(req.user.id, req.user.name, req.user.department, req.ip || 'unknown', req.get('User-Agent') || 'unknown');
        }
        catch (auditError) {
            logger_js_1.logger.warn('Failed to log logout audit:', auditError);
        }
        logger_js_1.logger.info(`User ${req.user.name} (${req.user.id}) logged out successfully. Session ID: ${sessionId}`);
        res.json({ message: 'Logged out successfully' });
    }
    catch (error) {
        logger_js_1.logger.error('Logout error:', error);
        res.status(500).json({ error: 'Logout failed' });
    }
});
exports.authRouter.post('/immediate-logout', async (req, res) => {
    try {
        const { userId, sessionId, reason, timestamp } = req.body;
        let timeString = 'unknown';
        try {
            if (timestamp && !isNaN(timestamp)) {
                timeString = new Date(Number(timestamp)).toISOString();
            }
            else {
                timeString = new Date().toISOString();
            }
        }
        catch (timeError) {
            timeString = new Date().toISOString();
        }
        logger_js_1.logger.info(`🚪 IMMEDIATE LOGOUT: User ${userId} (${reason}) at ${timeString}`);
        if (sessionId && sessionId !== 'unknown') {
            const sessionCheck = await (0, db_js_1.query)('SELECT id, user_id, user_name FROM active_sessions WHERE id = ? AND is_active = TRUE', [sessionId]);
            if (sessionCheck.length > 0) {
                await (0, db_js_1.query)('UPDATE active_sessions SET is_active = FALSE, logout_reason = ?, session_end = NOW() WHERE id = ?', [reason, sessionId]);
                logger_js_1.logger.info(`✅ IMMEDIATE LOGOUT: Session ${sessionId} found and deactivated`);
            }
            else {
                logger_js_1.logger.warn(`⚠️ IMMEDIATE LOGOUT: Session ${sessionId} not found or already inactive`);
            }
            try {
                const { releaseUserLocksOnLogout } = require('../socket/socketHandlers');
                const cleanedUp = await releaseUserLocksOnLogout(userId);
                logger_js_1.logger.info(`✅ IMMEDIATE LOGOUT: User ${userId} cleanup completed - ${cleanedUp} locks released`);
            }
            catch (lockError) {
                logger_js_1.logger.warn('Failed to cleanup locks on immediate logout:', lockError);
            }
        }
        else {
            logger_js_1.logger.warn(`⚠️ IMMEDIATE LOGOUT: No valid sessionId provided for user ${userId}`);
            try {
                const { releaseUserLocksOnLogout } = require('../socket/socketHandlers');
                const cleanedUp = await releaseUserLocksOnLogout(userId);
                logger_js_1.logger.info(`✅ IMMEDIATE LOGOUT: User ${userId} cleanup completed without session - ${cleanedUp} locks released`);
            }
            catch (lockError) {
                logger_js_1.logger.warn('Failed to cleanup locks on immediate logout:', lockError);
            }
        }
        res.status(200).json({ success: true });
    }
    catch (error) {
        logger_js_1.logger.error('❌ Immediate logout error:', error);
        res.status(200).json({ success: false, error: error instanceof Error ? error.message : String(error) });
    }
});
exports.authRouter.post('/heartbeat', auth_js_1.authenticate, async (req, res) => {
    try {
        const sessionId = req.headers['x-session-id'] || req.cookies?.vms_session_id;
        if (!sessionId) {
            return res.status(401).json({ error: 'No session found' });
        }
        await (0, db_js_1.query)('UPDATE active_sessions SET last_activity = NOW() WHERE id = ? AND is_active = TRUE', [sessionId]);
        logger_js_1.logger.debug(`Session heartbeat updated: ${sessionId}`);
        res.json({
            success: true,
            message: 'Session extended',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_js_1.logger.error('Session heartbeat error:', error);
        res.status(500).json({ error: 'Failed to update session' });
    }
});
exports.authRouter.get('/users-by-department', async (req, res) => {
    try {
        const users = await (0, db_js_1.query)(`
      SELECT id, name, department
      FROM users
      WHERE is_active = 1
        AND name NOT LIKE '%GUEST%'
        AND name NOT LIKE '%guest%'
        AND role NOT LIKE '%GUEST%'
      ORDER BY department, name
    `);
        logger_js_1.logger.info(`Fetched ${users.length} active users for login dropdown (GUEST accounts excluded)`);
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        res.json(users);
    }
    catch (error) {
        logger_js_1.logger.error('Get users by department error:', error);
        res.status(500).json({ error: 'Failed to get users' });
    }
});

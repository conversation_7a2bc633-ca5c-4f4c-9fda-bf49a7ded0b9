"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.simpleEventBus = void 0;
class SimpleEventBus {
    static instance;
    listeners = new Map();
    constructor() {
        console.log('🎯 Simple Event Bus initialized - Zero dependencies');
    }
    static getInstance() {
        if (!SimpleEventBus.instance) {
            SimpleEventBus.instance = new SimpleEventBus();
        }
        return SimpleEventBus.instance;
    }
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }
    emit(event, ...args) {
        const callbacks = this.listeners.get(event);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(...args);
                }
                catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }
    off(event, callback) {
        const callbacks = this.listeners.get(event);
        if (callbacks) {
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }
    emitBatchCreated(batchData) {
        console.log(`📡 Event: Batch created - ${batchData.id}`);
        this.emit('batch:created', batchData);
    }
    emitBatchUpdated(batchData) {
        console.log(`📡 Event: Batch updated - ${batchData.id}`);
        this.emit('batch:updated', batchData);
    }
    emitBatchReceived(batchData) {
        console.log(`📡 Event: Batch received - ${batchData.id}`);
        this.emit('batch:received', batchData);
    }
    emitNotificationCreated(notificationData) {
        console.log(`📡 Event: Notification created for ${notificationData.user_id}`);
        this.emit('notification:created', notificationData);
    }
    emitVoucherUpdated(voucherData) {
        console.log(`📡 Event: Voucher updated - ${voucherData.id}`);
        this.emit('voucher:updated', voucherData);
    }
}
exports.simpleEventBus = SimpleEventBus.getInstance();

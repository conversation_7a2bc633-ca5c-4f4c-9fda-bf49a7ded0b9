"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serverConnectionManager = void 0;
exports.healthCheckMiddleware = healthCheckMiddleware;
const logger_1 = require("../utils/logger");
class ServerConnectionManager {
    disconnectionTimers = new Map();
    GRACE_PERIOD = 10000;
    HEALTH_CHECK_THROTTLE = 30000;
    lastHealthCheck = new Map();
    handleDisconnection(userId, userName, department, socketId, cleanupCallback) {
        const timerKey = `${userId}-${socketId}`;
        this.cancelDisconnectionTimer(timerKey);
        logger_1.logger.info(`🔄 RESILIENCE: Starting ${this.GRACE_PERIOD / 1000}s grace period for ${userName} (${userId})`);
        const timer = setTimeout(async () => {
            logger_1.logger.info(`⏰ RESILIENCE: Grace period expired for ${userName} (${userId}) - proceeding with cleanup`);
            try {
                await cleanupCallback();
                this.disconnectionTimers.delete(timerKey);
            }
            catch (error) {
                logger_1.logger.error(`❌ RESILIENCE: Cleanup failed for ${userName}:`, error);
            }
        }, this.GRACE_PERIOD);
        this.disconnectionTimers.set(timerKey, {
            userId,
            userName,
            department,
            timer
        });
    }
    cancelDisconnectionTimer(timerKey) {
        const existingTimer = this.disconnectionTimers.get(timerKey);
        if (existingTimer) {
            clearTimeout(existingTimer.timer);
            this.disconnectionTimers.delete(timerKey);
            logger_1.logger.info(`✅ RESILIENCE: Cancelled disconnection timer for ${existingTimer.userName}`);
            return true;
        }
        return false;
    }
    handleReconnection(userId, userName, socketId) {
        const timerKey = `${userId}-${socketId}`;
        const cancelled = this.cancelDisconnectionTimer(timerKey);
        if (cancelled) {
            logger_1.logger.info(`🔄 RESILIENCE: User ${userName} (${userId}) reconnected during grace period`);
        }
        else {
            logger_1.logger.info(`🔄 RESILIENCE: User ${userName} (${userId}) connected (no pending disconnection)`);
        }
    }
    shouldAllowHealthCheck(clientId) {
        const now = Date.now();
        const lastCheck = this.lastHealthCheck.get(clientId) || 0;
        if (now - lastCheck >= this.HEALTH_CHECK_THROTTLE) {
            this.lastHealthCheck.set(clientId, now);
            return true;
        }
        return false;
    }
    cleanupHealthCheckRecords() {
        const now = Date.now();
        const expiredThreshold = this.HEALTH_CHECK_THROTTLE * 2;
        for (const [clientId, lastCheck] of this.lastHealthCheck.entries()) {
            if (now - lastCheck > expiredThreshold) {
                this.lastHealthCheck.delete(clientId);
            }
        }
    }
    getConnectionStats() {
        return {
            pendingDisconnections: this.disconnectionTimers.size,
            healthCheckClients: this.lastHealthCheck.size,
            gracePeriodMs: this.GRACE_PERIOD,
            healthCheckThrottleMs: this.HEALTH_CHECK_THROTTLE
        };
    }
    cleanup() {
        logger_1.logger.info('🧹 RESILIENCE: Cleaning up all connection timers');
        for (const [timerKey, timerData] of this.disconnectionTimers.entries()) {
            clearTimeout(timerData.timer);
            logger_1.logger.info(`🧹 RESILIENCE: Cleared timer for ${timerData.userName}`);
        }
        this.disconnectionTimers.clear();
        this.lastHealthCheck.clear();
    }
}
exports.serverConnectionManager = new ServerConnectionManager();
function healthCheckMiddleware(req, res, next) {
    const clientId = req.ip + '-' + (req.headers['user-agent'] || 'unknown');
    if (req.path === '/api/health' || req.path === '/health') {
        if (!exports.serverConnectionManager.shouldAllowHealthCheck(clientId)) {
            return res.status(200).json({
                status: 'healthy',
                cached: true,
                timestamp: new Date().toISOString()
            });
        }
    }
    next();
}
setInterval(() => {
    exports.serverConnectionManager.cleanupHealthCheckRecords();
}, 60000);

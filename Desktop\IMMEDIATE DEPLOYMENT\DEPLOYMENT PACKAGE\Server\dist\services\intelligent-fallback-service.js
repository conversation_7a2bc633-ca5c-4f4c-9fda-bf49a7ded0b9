"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.intelligentFallbackService = exports.IntelligentFallbackService = void 0;
const events_1 = require("events");
const hybrid_network_service_js_1 = require("./hybrid-network-service.js");
const network_discovery_service_js_1 = require("./network-discovery-service.js");
const logger_js_1 = require("../utils/logger.js");
class IntelligentFallbackService extends events_1.EventEmitter {
    _isRunning = false;
    _fallbackRules = [];
    _decisionHistory = [];
    _ruleCooldowns = new Map();
    _stats = {
        totalDecisions: 0,
        staticToDynamic: 0,
        dynamicToStatic: 0,
        discoveryActivations: 0,
        lastDecision: null,
        averageDecisionTime: 0,
        successRate: 0
    };
    constructor() {
        super();
        this.initializeDefaultRules();
        logger_js_1.logger.info('🧠 Intelligent Fallback Service initialized');
    }
    async start() {
        try {
            if (this._isRunning) {
                logger_js_1.logger.warn('Intelligent Fallback Service is already running');
                return;
            }
            logger_js_1.logger.info('🚀 Starting Intelligent Fallback System...');
            hybrid_network_service_js_1.hybridNetworkService.on('networkChange', this.handleNetworkChange.bind(this));
            this.startPeriodicEvaluation();
            this._isRunning = true;
            logger_js_1.logger.info('✅ Intelligent Fallback System started successfully');
        }
        catch (error) {
            logger_js_1.logger.error('❌ Failed to start Intelligent Fallback Service:', error);
            throw error;
        }
    }
    async stop() {
        try {
            if (!this._isRunning) {
                return;
            }
            logger_js_1.logger.info('🛑 Stopping Intelligent Fallback System...');
            hybrid_network_service_js_1.hybridNetworkService.removeAllListeners('networkChange');
            this._isRunning = false;
            logger_js_1.logger.info('✅ Intelligent Fallback System stopped');
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error stopping Intelligent Fallback Service:', error);
        }
    }
    initializeDefaultRules() {
        this._fallbackRules = [
            {
                id: 'static_ip_failed',
                name: 'Static IP Connection Failed',
                condition: (config, event) => event?.type === 'static_failed' && config.isDynamicAvailable,
                action: 'switch_to_dynamic',
                priority: 100,
                cooldownMs: 30000,
                description: 'Switch to dynamic mode when static IP fails'
            },
            {
                id: 'static_ip_restored',
                name: 'Static IP Connection Restored',
                condition: (config, event) => event?.type === 'static_restored' && config.mode === 'dynamic',
                action: 'switch_to_static',
                priority: 90,
                cooldownMs: 60000,
                description: 'Switch back to static mode when connection is restored'
            },
            {
                id: 'network_changed_major',
                name: 'Major Network Configuration Change',
                condition: (config, event) => {
                    if (event?.type !== 'network_changed')
                        return false;
                    const oldIP = event.oldConfig.currentIP;
                    const newIP = event.newConfig.currentIP;
                    if (!oldIP || !newIP)
                        return false;
                    const oldSubnet = oldIP.split('.').slice(0, 3).join('.');
                    const newSubnet = newIP.split('.').slice(0, 3).join('.');
                    return oldSubnet !== newSubnet;
                },
                action: 'force_discovery',
                priority: 80,
                cooldownMs: 120000,
                description: 'Force discovery when network subnet changes'
            },
            {
                id: 'dynamic_mode_stable',
                name: 'Dynamic Mode Stable Performance',
                condition: (config) => config.mode === 'dynamic' &&
                    config.isDynamicAvailable &&
                    !config.isStaticAvailable &&
                    this.isNetworkStable(config),
                action: 'maintain_current',
                priority: 70,
                cooldownMs: 300000,
                description: 'Maintain dynamic mode when stable and static unavailable'
            },
            {
                id: 'static_mode_preferred',
                name: 'Static Mode Preferred When Available',
                condition: (config) => config.mode === 'dynamic' &&
                    config.isStaticAvailable &&
                    this.isNetworkStable(config),
                action: 'switch_to_static',
                priority: 60,
                cooldownMs: 180000,
                description: 'Prefer static mode when available and network is stable'
            },
            {
                id: 'discovery_health_check',
                name: 'Network Discovery Health Check',
                condition: (config) => !config.isDynamicAvailable &&
                    config.mode === 'dynamic',
                action: 'force_discovery',
                priority: 50,
                cooldownMs: 60000,
                description: 'Force discovery restart when dynamic mode is unhealthy'
            }
        ];
        logger_js_1.logger.info(`📋 Initialized ${this._fallbackRules.length} fallback rules`);
    }
    async handleNetworkChange(event) {
        try {
            logger_js_1.logger.info(`🔄 Processing network change event: ${event.type}`);
            const startTime = Date.now();
            const decision = await this.evaluateNetworkChange(event);
            const decisionTime = Date.now() - startTime;
            if (decision) {
                await this.executeDecision(decision);
                this.updateStats(decision, decisionTime);
                this.emit('fallbackDecision', decision);
            }
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error handling network change:', error);
        }
    }
    async evaluateNetworkChange(event) {
        try {
            const config = hybrid_network_service_js_1.hybridNetworkService.getNetworkConfiguration();
            const applicableRules = this._fallbackRules
                .filter(rule => this.isRuleApplicable(rule, config, event))
                .filter(rule => !this.isRuleInCooldown(rule.id))
                .sort((a, b) => b.priority - a.priority);
            if (applicableRules.length === 0) {
                logger_js_1.logger.debug('No applicable fallback rules found');
                return null;
            }
            const selectedRule = applicableRules[0];
            const confidence = this.calculateDecisionConfidence(selectedRule, config, event);
            const decision = {
                ruleId: selectedRule.id,
                ruleName: selectedRule.name,
                action: selectedRule.action,
                reason: selectedRule.description,
                confidence,
                timestamp: new Date(),
                networkConfig: config
            };
            this._ruleCooldowns.set(selectedRule.id, Date.now() + selectedRule.cooldownMs);
            logger_js_1.logger.info(`🎯 Fallback decision: ${selectedRule.name} -> ${selectedRule.action} (${confidence}% confidence)`);
            return decision;
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error evaluating network change:', error);
            return null;
        }
    }
    async executeDecision(decision) {
        try {
            logger_js_1.logger.info(`⚡ Executing fallback action: ${decision.action}`);
            let success = false;
            switch (decision.action) {
                case 'switch_to_static':
                    success = await hybrid_network_service_js_1.hybridNetworkService.switchToStaticMode();
                    break;
                case 'switch_to_dynamic':
                    success = await hybrid_network_service_js_1.hybridNetworkService.switchToDynamicModeManual();
                    break;
                case 'force_discovery':
                    await this.forceNetworkDiscovery();
                    success = true;
                    break;
                case 'maintain_current':
                    logger_js_1.logger.info('💡 Maintaining current network mode');
                    success = true;
                    break;
                default:
                    logger_js_1.logger.warn(`⚠️ Unknown fallback action: ${decision.action}`);
                    success = false;
            }
            if (success) {
                logger_js_1.logger.info(`✅ Fallback action executed successfully: ${decision.action}`);
            }
            else {
                logger_js_1.logger.warn(`⚠️ Fallback action failed: ${decision.action}`);
            }
            this._decisionHistory.push(decision);
            if (this._decisionHistory.length > 100) {
                this._decisionHistory = this._decisionHistory.slice(-100);
            }
        }
        catch (error) {
            logger_js_1.logger.error(`❌ Error executing fallback decision ${decision.action}:`, error);
        }
    }
    async forceNetworkDiscovery() {
        try {
            logger_js_1.logger.info('🔄 Forcing network discovery restart...');
            await network_discovery_service_js_1.networkDiscoveryService.stop();
            await new Promise(resolve => setTimeout(resolve, 2000));
            await network_discovery_service_js_1.networkDiscoveryService.start();
            logger_js_1.logger.info('✅ Network discovery restarted');
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error forcing network discovery:', error);
        }
    }
    startPeriodicEvaluation() {
        setInterval(async () => {
            try {
                if (!this._isRunning)
                    return;
                const config = hybrid_network_service_js_1.hybridNetworkService.getNetworkConfiguration();
                const decision = await this.evaluatePeriodicConditions(config);
                if (decision) {
                    await this.executeDecision(decision);
                    this.updateStats(decision, 0);
                    this.emit('fallbackDecision', decision);
                }
            }
            catch (error) {
                logger_js_1.logger.error('❌ Error in periodic evaluation:', error);
            }
        }, 60000);
        logger_js_1.logger.info('⏰ Periodic evaluation started (60s intervals)');
    }
    async evaluatePeriodicConditions(config) {
        const periodicRules = this._fallbackRules
            .filter(rule => this.isRuleApplicable(rule, config))
            .filter(rule => !this.isRuleInCooldown(rule.id))
            .sort((a, b) => b.priority - a.priority);
        if (periodicRules.length === 0) {
            return null;
        }
        const selectedRule = periodicRules[0];
        const confidence = this.calculateDecisionConfidence(selectedRule, config);
        if (confidence < 70) {
            return null;
        }
        this._ruleCooldowns.set(selectedRule.id, Date.now() + selectedRule.cooldownMs);
        return {
            ruleId: selectedRule.id,
            ruleName: selectedRule.name,
            action: selectedRule.action,
            reason: `Periodic check: ${selectedRule.description}`,
            confidence,
            timestamp: new Date(),
            networkConfig: config
        };
    }
    isRuleApplicable(rule, config, event) {
        try {
            return rule.condition(config, event);
        }
        catch (error) {
            logger_js_1.logger.warn(`⚠️ Error evaluating rule ${rule.id}:`, error);
            return false;
        }
    }
    isRuleInCooldown(ruleId) {
        const cooldownEnd = this._ruleCooldowns.get(ruleId);
        return cooldownEnd ? Date.now() < cooldownEnd : false;
    }
    calculateDecisionConfidence(rule, config, event) {
        let confidence = rule.priority;
        if (this.isNetworkStable(config)) {
            confidence += 10;
        }
        else {
            confidence -= 10;
        }
        if (event) {
            switch (event.type) {
                case 'static_failed':
                case 'static_restored':
                    confidence += 20;
                    break;
                case 'network_changed':
                    confidence += 10;
                    break;
                default:
                    confidence += 5;
            }
        }
        const recentDecisions = this._decisionHistory.slice(-10);
        const successRate = recentDecisions.length > 0
            ? recentDecisions.filter(d => d.ruleId === rule.id).length / recentDecisions.length
            : 0.5;
        confidence += (successRate - 0.5) * 20;
        return Math.max(0, Math.min(100, confidence));
    }
    isNetworkStable(config) {
        const timeSinceLastChange = Date.now() - config.lastNetworkChange.getTime();
        return timeSinceLastChange > 120000;
    }
    updateStats(decision, decisionTime) {
        this._stats.totalDecisions++;
        this._stats.lastDecision = decision;
        this._stats.averageDecisionTime =
            (this._stats.averageDecisionTime * (this._stats.totalDecisions - 1) + decisionTime) /
                this._stats.totalDecisions;
        switch (decision.action) {
            case 'switch_to_static':
                this._stats.dynamicToStatic++;
                break;
            case 'switch_to_dynamic':
                this._stats.staticToDynamic++;
                break;
            case 'force_discovery':
                this._stats.discoveryActivations++;
                break;
        }
        const recentDecisions = this._decisionHistory.slice(-20);
        const avgConfidence = recentDecisions.reduce((sum, d) => sum + d.confidence, 0) / recentDecisions.length;
        this._stats.successRate = avgConfidence || 0;
    }
    getStats() {
        return { ...this._stats };
    }
    getDecisionHistory(limit = 50) {
        return this._decisionHistory.slice(-limit);
    }
    addCustomRule(rule) {
        this._fallbackRules.push(rule);
        this._fallbackRules.sort((a, b) => b.priority - a.priority);
        logger_js_1.logger.info(`📋 Added custom fallback rule: ${rule.name}`);
    }
    removeRule(ruleId) {
        const index = this._fallbackRules.findIndex(rule => rule.id === ruleId);
        if (index !== -1) {
            this._fallbackRules.splice(index, 1);
            logger_js_1.logger.info(`🗑️ Removed fallback rule: ${ruleId}`);
            return true;
        }
        return false;
    }
    isRunning() {
        return this._isRunning;
    }
}
exports.IntelligentFallbackService = IntelligentFallbackService;
exports.intelligentFallbackService = new IntelligentFallbackService();

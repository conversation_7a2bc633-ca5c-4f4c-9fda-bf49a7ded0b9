"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.notificationRouter = void 0;
const express_1 = __importDefault(require("express"));
const uuid_1 = require("uuid");
const db_js_1 = require("../database/db.js");
const auth_js_1 = require("../middleware/auth.js");
const logger_js_1 = require("../utils/logger.js");
exports.notificationRouter = express_1.default.Router();
exports.notificationRouter.use(auth_js_1.authenticate);
exports.notificationRouter.get('/', async (req, res) => {
    try {
        let notifications;
        if (req.user.department === 'SYSTEM ADMIN') {
            notifications = await (0, db_js_1.query)('SELECT * FROM notifications ORDER BY timestamp DESC');
        }
        else if (req.user.department === 'AUDIT') {
            notifications = await (0, db_js_1.query)('SELECT * FROM notifications WHERE user_id = ? OR user_id = ? OR from_audit = FALSE ORDER BY timestamp DESC', [req.user.id, 'AUDIT']);
        }
        else {
            notifications = await (0, db_js_1.query)('SELECT * FROM notifications WHERE user_id = ? OR user_id = ? OR (from_audit = TRUE AND user_id = ?) ORDER BY timestamp DESC', [req.user.id, req.user.department, req.user.department]);
        }
        res.json(notifications);
    }
    catch (error) {
        logger_js_1.logger.error('Get notifications error:', error);
        res.status(500).json({ error: 'Failed to get notifications' });
    }
});
exports.notificationRouter.get('/unread/count', async (req, res) => {
    try {
        let count;
        if (req.user.department === 'SYSTEM ADMIN') {
            const result = await (0, db_js_1.query)('SELECT COUNT(*) as count FROM notifications WHERE is_read = FALSE');
            count = result[0].count;
        }
        else if (req.user.department === 'AUDIT') {
            const result = await (0, db_js_1.query)('SELECT COUNT(*) as count FROM notifications WHERE is_read = FALSE AND (user_id = ? OR user_id = ? OR from_audit = FALSE)', [req.user.id, 'AUDIT']);
            count = result[0].count;
        }
        else {
            const result = await (0, db_js_1.query)('SELECT COUNT(*) as count FROM notifications WHERE is_read = FALSE AND (user_id = ? OR user_id = ? OR (from_audit = TRUE AND user_id = ?))', [req.user.id, req.user.department, req.user.department]);
            count = result[0].count;
        }
        res.json({ count });
    }
    catch (error) {
        logger_js_1.logger.error('Get unread notifications count error:', error);
        res.status(500).json({ error: 'Failed to get unread notifications count' });
    }
});
exports.notificationRouter.put('/:id/read', async (req, res) => {
    try {
        const notificationId = req.params.id;
        const notifications = await (0, db_js_1.query)('SELECT * FROM notifications WHERE id = ?', [notificationId]);
        if (notifications.length === 0) {
            return res.status(404).json({ error: 'Notification not found' });
        }
        const notification = notifications[0];
        if (req.user.department !== 'SYSTEM ADMIN' &&
            notification.user_id !== req.user.id &&
            notification.user_id !== req.user.department &&
            !(req.user.department === 'AUDIT' && notification.from_audit === false) &&
            !(notification.from_audit === true && notification.user_id === req.user.department)) {
            return res.status(403).json({ error: 'Access denied' });
        }
        await (0, db_js_1.query)('UPDATE notifications SET is_read = TRUE WHERE id = ?', [notificationId]);
        const updatedNotifications = await (0, db_js_1.query)('SELECT * FROM notifications WHERE id = ?', [notificationId]);
        res.json(updatedNotifications[0]);
    }
    catch (error) {
        logger_js_1.logger.error('Mark notification as read error:', error);
        res.status(500).json({ error: 'Failed to mark notification as read' });
    }
});
exports.notificationRouter.put('/read/all', async (req, res) => {
    try {
        if (req.user.department === 'SYSTEM ADMIN') {
            await (0, db_js_1.query)('UPDATE notifications SET is_read = TRUE');
        }
        else if (req.user.department === 'AUDIT') {
            await (0, db_js_1.query)('UPDATE notifications SET is_read = TRUE WHERE user_id = ? OR user_id = ? OR from_audit = FALSE', [req.user.id, 'AUDIT']);
        }
        else {
            await (0, db_js_1.query)('UPDATE notifications SET is_read = TRUE WHERE user_id = ? OR user_id = ? OR (from_audit = TRUE AND user_id = ?)', [req.user.id, req.user.department, req.user.department]);
        }
        res.json({ message: 'All notifications marked as read' });
    }
    catch (error) {
        logger_js_1.logger.error('Mark all notifications as read error:', error);
        res.status(500).json({ error: 'Failed to mark all notifications as read' });
    }
});
exports.notificationRouter.post('/', async (req, res) => {
    try {
        const { userId, message, voucherId, batchId, type, fromAudit = false } = req.body;
        if (!userId || !message || !type) {
            return res.status(400).json({ error: 'User ID, message, and type are required' });
        }
        const id = (0, uuid_1.v4)();
        await (0, db_js_1.query)(`INSERT INTO notifications (
        id, user_id, message, is_read, timestamp, voucher_id, batch_id, type, from_audit
      ) VALUES (?, ?, ?, ?, NOW(), ?, ?, ?, ?)`, [id, userId, message, false, voucherId || null, batchId || null, type, fromAudit]);
        const notifications = await (0, db_js_1.query)('SELECT * FROM notifications WHERE id = ?', [id]);
        res.status(201).json(notifications[0]);
    }
    catch (error) {
        logger_js_1.logger.error('Create notification error:', error);
        res.status(500).json({ error: 'Failed to create notification' });
    }
});
exports.notificationRouter.delete('/:id', async (req, res) => {
    try {
        const notificationId = req.params.id;
        const notifications = await (0, db_js_1.query)('SELECT * FROM notifications WHERE id = ?', [notificationId]);
        if (notifications.length === 0) {
            return res.status(404).json({ error: 'Notification not found' });
        }
        const notification = notifications[0];
        if (req.user.department !== 'SYSTEM ADMIN' &&
            notification.user_id !== req.user.id &&
            notification.user_id !== req.user.department) {
            return res.status(403).json({ error: 'Access denied' });
        }
        await (0, db_js_1.query)('DELETE FROM notifications WHERE id = ?', [notificationId]);
        res.json({ message: 'Notification deleted successfully' });
    }
    catch (error) {
        logger_js_1.logger.error('Delete notification error:', error);
        res.status(500).json({ error: 'Failed to delete notification' });
    }
});
